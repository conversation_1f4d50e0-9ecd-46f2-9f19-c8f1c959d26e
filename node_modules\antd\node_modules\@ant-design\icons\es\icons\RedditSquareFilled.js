import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import RedditSquareFilledSvg from "@ant-design/icons-svg/es/asn/RedditSquareFilled";
import AntdIcon from "../components/AntdIcon";
var RedditSquareFilled = function RedditSquareFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: RedditSquareFilledSvg
  }));
};

/**![reddit-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI5NiA0NDBhMzUuOTggMzUuOTggMCAwMC0xMy40IDY5LjRjMTEuNS0xOC4xIDI3LjEtMzQuNSA0NS45LTQ4LjhBMzUuOSAzNS45IDAgMDAyOTYgNDQwem0yODkuNyAxODQuOWMtMTQuOSAxMS43LTQ0LjMgMjQuMy03My43IDI0LjNzLTU4LjktMTIuNi03My43LTI0LjNjLTkuMy03LjMtMjIuNy01LjctMzAgMy42LTcuMyA5LjMtNS43IDIyLjcgMy42IDMwIDI1LjcgMjAuNCA2NSAzMy41IDEwMC4xIDMzLjUgMzUuMSAwIDc0LjQtMTMuMSAxMDAuMi0zMy41IDkuMy03LjMgMTAuOS0yMC44IDMuNi0zMGEyMS40NiAyMS40NiAwIDAwLTMwLjEtMy42ek04ODAgMTEySDE0NGMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2NzM2YzAgMTcuNyAxNC4zIDMyIDMyIDMyaDczNmMxNy43IDAgMzItMTQuMyAzMi0zMlYxNDRjMC0xNy43LTE0LjMtMzItMzItMzJ6TTc1NyA1NDEuOWM0LjYgMTMuNSA3IDI3LjYgNyA0Mi4xIDAgOTkuNC0xMTIuOCAxODAtMjUyIDE4MHMtMjUyLTgwLjYtMjUyLTE4MGMwLTE0LjUgMi40LTI4LjYgNy00Mi4xQTcyLjAxIDcyLjAxIDAgMDEyOTYgNDA0YzI3LjEgMCA1MC42IDE0LjkgNjIuOSAzNyAzNi4yLTE5LjggODAuMi0zMi44IDEyOC4xLTM2LjFsNTguNC0xMzEuMWM0LjMtOS44IDE1LjItMTQuOCAyNS41LTExLjhsOTEuNiAyNi41YTU0LjAzIDU0LjAzIDAgMDExMDEuNiAyNS42YzAgMjkuOC0yNC4yIDU0LTU0IDU0LTIzLjUgMC00My41LTE1LjEtNTAuOS0zNi4xTDU3NyAzMDguM2wtNDMgOTYuNWM0OS4xIDMgOTQuMiAxNi4xIDEzMS4yIDM2LjMgMTIuMy0yMi4xIDM1LjgtMzcgNjIuOS0zNyAzOS44IDAgNzIgMzIuMiA3MiA3Mi0uMSAyOS4zLTE3LjggNTQuNi00My4xIDY1Ljh6TTU4NCA1NDhhMzYgMzYgMCAxMDcyIDAgMzYgMzYgMCAxMC03MiAwem0xNDQtMTA4YTM1LjkgMzUuOSAwIDAwLTMyLjUgMjAuNmMxOC44IDE0LjMgMzQuNCAzMC43IDQ1LjkgNDguOEEzNS45OCAzNS45OCAwIDAwNzI4IDQ0MHpNMzY4IDU0OGEzNiAzNiAwIDEwNzIgMCAzNiAzNiAwIDEwLTcyIDB6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(RedditSquareFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'RedditSquareFilled';
}
export default RefIcon;