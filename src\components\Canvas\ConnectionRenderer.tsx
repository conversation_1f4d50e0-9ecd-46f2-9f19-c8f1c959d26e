import React from 'react';
import { Line, Circle } from 'react-konva';
import { Connection, BaseNode } from '../../types';
import { getBezierControlPoints } from '../../utils/canvasUtils';

interface ConnectionRendererProps {
  connection: Connection;
  sourceNode: BaseNode;
  targetNode: BaseNode;
  isSelected?: boolean;
  onSelect?: (connectionId: string) => void;
}

const ConnectionRenderer: React.FC<ConnectionRendererProps> = ({
  connection,
  sourceNode,
  targetNode,
  isSelected = false,
  onSelect
}) => {
  // 计算连接点位置
  const getConnectionPoint = (
    node: BaseNode,
    connectionId: string,
    type: 'input' | 'output'
  ) => {
    const connections = type === 'input' ? node.inputs : node.outputs;
    const connectionIndex = connections.findIndex(conn => conn.id === connectionId);
    
    const headerHeight = 24;
    const connectionSpacing = 20;
    const connectionRadius = 6;
    
    const x = type === 'input' 
      ? node.position.x 
      : node.position.x + node.size.width;
    const y = node.position.y + headerHeight + 16 + connectionIndex * connectionSpacing;
    
    return { x, y };
  };

  const sourcePoint = getConnectionPoint(sourceNode, connection.sourceConnectionId, 'output');
  const targetPoint = getConnectionPoint(targetNode, connection.targetConnectionId, 'input');
  
  // 计算贝塞尔曲线控制点
  const { cp1, cp2 } = getBezierControlPoints(sourcePoint, targetPoint, 0.4);
  
  // 获取连接线颜色
  const getConnectionColor = () => {
    const sourceConnection = sourceNode.outputs.find(
      output => output.id === connection.sourceConnectionId
    );
    
    if (!sourceConnection) return '#666';
    
    const colors: Record<string, string> = {
      'image': '#52c41a',
      'number': '#1890ff',
      'color': '#fa8c16',
      'boolean': '#722ed1'
    };
    
    return colors[sourceConnection.dataType] || '#666';
  };

  const connectionColor = getConnectionColor();
  
  const handleClick = (e: any) => {
    e.cancelBubble = true;
    if (onSelect) {
      onSelect(connection.id);
    }
  };

  // 生成贝塞尔曲线路径点
  const generateCurvePoints = () => {
    const points: number[] = [];
    const steps = 50;
    
    for (let i = 0; i <= steps; i++) {
      const t = i / steps;
      const x = Math.pow(1 - t, 3) * sourcePoint.x +
                3 * Math.pow(1 - t, 2) * t * cp1.x +
                3 * (1 - t) * Math.pow(t, 2) * cp2.x +
                Math.pow(t, 3) * targetPoint.x;
      const y = Math.pow(1 - t, 3) * sourcePoint.y +
                3 * Math.pow(1 - t, 2) * t * cp1.y +
                3 * (1 - t) * Math.pow(t, 2) * cp2.y +
                Math.pow(t, 3) * targetPoint.y;
      points.push(x, y);
    }
    
    return points;
  };

  const curvePoints = generateCurvePoints();

  return (
    <>
      {/* 连接线背景（用于更容易点击） */}
      <Line
        points={curvePoints}
        stroke="transparent"
        strokeWidth={12}
        onClick={handleClick}
        listening={true}
      />
      
      {/* 主连接线 */}
      <Line
        points={curvePoints}
        stroke={isSelected ? '#1890ff' : connectionColor}
        strokeWidth={isSelected ? 3 : 2}
        tension={0}
        lineCap="round"
        lineJoin="round"
        shadowColor="rgba(0, 0, 0, 0.1)"
        shadowBlur={2}
        shadowOffset={{ x: 0, y: 1 }}
        onClick={handleClick}
        listening={false}
      />
      
      {/* 选中状态的虚线边框 */}
      {isSelected && (
        <Line
          points={curvePoints}
          stroke="#1890ff"
          strokeWidth={1}
          tension={0}
          dash={[5, 5]}
          lineCap="round"
          lineJoin="round"
          listening={false}
        />
      )}
      
      {/* 连接线中点的控制点（选中时显示） */}
      {isSelected && (
        <Circle
          x={(sourcePoint.x + targetPoint.x) / 2}
          y={(sourcePoint.y + targetPoint.y) / 2}
          radius={4}
          fill="#1890ff"
          stroke="#fff"
          strokeWidth={2}
        />
      )}
      
      {/* 数据流动画效果 */}
      <Line
        points={curvePoints}
        stroke={connectionColor}
        strokeWidth={1}
        tension={0}
        dash={[4, 8]}
        lineCap="round"
        opacity={0.6}
        listening={false}
      />
    </>
  );
};

export default ConnectionRenderer;
