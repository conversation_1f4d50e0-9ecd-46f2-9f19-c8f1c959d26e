/* 应用主布局样式 */
.app-layout {
  height: 100vh;
  overflow: hidden;
}

.app-header {
  padding: 0;
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  z-index: 100;
}

.app-content {
  height: calc(100vh - 64px - 32px); /* 减去header和footer高度 */
}

.app-sider-left {
  border-right: 1px solid #e8e8e8;
  background: #fafafa;
}

.app-sider-right {
  border-left: 1px solid #e8e8e8;
  background: #fafafa;
}

.app-main-content {
  background: #f5f5f5;
  position: relative;
  overflow: hidden;
}

.app-footer {
  height: 32px;
  padding: 0 16px;
  line-height: 32px;
  background: #fff;
  border-top: 1px solid #e8e8e8;
  font-size: 12px;
  color: #666;
}

/* 全局样式重置 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', '<PERSON>ra Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 禁用文本选择 */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 拖拽相关样式 */
.dragging {
  cursor: grabbing !important;
}

.drag-over {
  background-color: rgba(24, 144, 255, 0.1);
  border: 2px dashed #1890ff;
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-left {
  animation: slideInLeft 0.3s ease-out;
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .app-sider-left {
    width: 260px !important;
  }

  .app-sider-right {
    width: 300px !important;
  }
}

@media (max-width: 1200px) {
  .app-sider-left {
    width: 240px !important;
  }

  .app-sider-right {
    width: 280px !important;
  }
}

@media (max-width: 1024px) {
  .app-sider-left {
    width: 200px !important;
  }

  .app-sider-right {
    width: 240px !important;
  }
}

/* 可折叠侧边栏 */
.app-sider-collapsed {
  width: 0 !important;
  min-width: 0 !important;
  max-width: 0 !important;
  overflow: hidden;
}

/* 侧边栏切换按钮 */
.sider-toggle-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 101;
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  width: 24px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.sider-toggle-btn:hover {
  background: #f0f0f0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.sider-toggle-btn-left {
  right: -12px;
}

.sider-toggle-btn-right {
  left: -12px;
}

/* 全屏模式 */
.app-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background: #fff;
}

.app-fullscreen .app-header,
.app-fullscreen .app-footer {
  display: none;
}

.app-fullscreen .app-content {
  height: 100vh;
}

/* 加载状态 */
.app-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.app-loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
