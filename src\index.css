/* 全局样式重置 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
  color: #333;
}

#root {
  height: 100vh;
  overflow: hidden;
}

/* 禁用文本选择 */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 拖拽相关样式 */
.dragging {
  cursor: grabbing !important;
}

.drag-over {
  background-color: rgba(24, 144, 255, 0.1);
  border: 2px dashed #1890ff;
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Ant Design 主题定制 */
.ant-layout {
  background: #fff;
}

.ant-layout-header {
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
}

.ant-layout-sider {
  background: #fafafa;
}

.ant-layout-content {
  background: #f5f5f5;
}

.ant-layout-footer {
  background: #fff;
  border-top: 1px solid #e8e8e8;
}
