var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
import React from 'react';
import { Group, Text } from 'react-konva';
import { TextEditor } from './text-editor';
export const EditorSt = React.forwardRef((props) => {
    const { text, onChange } = props, rest = __rest(props, ["text", "onChange"]);
    const [editorEnabled, setEditorEnabled] = React.useState(false);
    const textRef = React.useRef(null);
    return (React.createElement(Group, { draggable: true },
        React.createElement(Text, Object.assign({ text: text, ref: textRef, width: 100, onClick: () => {
                setEditorEnabled(true);
            }, visible: !editorEnabled }, rest)),
        editorEnabled && (React.createElement(Group, null,
            React.createElement(TextEditor, { value: text, textNodeRef: textRef, onChange: onChange, onBlur: () => {
                    setEditorEnabled(false);
                } })))));
});
