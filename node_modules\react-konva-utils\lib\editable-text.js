"use strict";
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EditorSt = void 0;
const react_1 = __importDefault(require("react"));
const react_konva_1 = require("react-konva");
const text_editor_1 = require("./text-editor");
exports.EditorSt = react_1.default.forwardRef((props) => {
    const { text, onChange } = props, rest = __rest(props, ["text", "onChange"]);
    const [editorEnabled, setEditorEnabled] = react_1.default.useState(false);
    const textRef = react_1.default.useRef(null);
    return (react_1.default.createElement(react_konva_1.Group, { draggable: true },
        react_1.default.createElement(react_konva_1.Text, Object.assign({ text: text, ref: textRef, width: 100, onClick: () => {
                setEditorEnabled(true);
            }, visible: !editorEnabled }, rest)),
        editorEnabled && (react_1.default.createElement(react_konva_1.Group, null,
            react_1.default.createElement(text_editor_1.TextEditor, { value: text, textNodeRef: textRef, onChange: onChange, onBlur: () => {
                    setEditorEnabled(false);
                } })))));
});
