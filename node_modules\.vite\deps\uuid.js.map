{"version": 3, "sources": ["../../uuid/dist/esm-browser/max.js", "../../uuid/dist/esm-browser/nil.js", "../../uuid/dist/esm-browser/regex.js", "../../uuid/dist/esm-browser/validate.js", "../../uuid/dist/esm-browser/parse.js", "../../uuid/dist/esm-browser/stringify.js", "../../uuid/dist/esm-browser/rng.js", "../../uuid/dist/esm-browser/v1.js", "../../uuid/dist/esm-browser/v1ToV6.js", "../../uuid/dist/esm-browser/md5.js", "../../uuid/dist/esm-browser/v35.js", "../../uuid/dist/esm-browser/v3.js", "../../uuid/dist/esm-browser/native.js", "../../uuid/dist/esm-browser/v4.js", "../../uuid/dist/esm-browser/sha1.js", "../../uuid/dist/esm-browser/v5.js", "../../uuid/dist/esm-browser/v6.js", "../../uuid/dist/esm-browser/v6ToV1.js", "../../uuid/dist/esm-browser/v7.js", "../../uuid/dist/esm-browser/version.js"], "sourcesContent": ["export default 'ffffffff-ffff-ffff-ffff-ffffffffffff';\n", "export default '00000000-0000-0000-0000-000000000000';\n", "export default /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i;\n", "import REGEX from './regex.js';\nfunction validate(uuid) {\n    return typeof uuid === 'string' && REGEX.test(uuid);\n}\nexport default validate;\n", "import validate from './validate.js';\nfunction parse(uuid) {\n    if (!validate(uuid)) {\n        throw TypeError('Invalid UUID');\n    }\n    let v;\n    return Uint8Array.of((v = parseInt(uuid.slice(0, 8), 16)) >>> 24, (v >>> 16) & 0xff, (v >>> 8) & 0xff, v & 0xff, (v = parseInt(uuid.slice(9, 13), 16)) >>> 8, v & 0xff, (v = parseInt(uuid.slice(14, 18), 16)) >>> 8, v & 0xff, (v = parseInt(uuid.slice(19, 23), 16)) >>> 8, v & 0xff, ((v = parseInt(uuid.slice(24, 36), 16)) / 0x10000000000) & 0xff, (v / 0x100000000) & 0xff, (v >>> 24) & 0xff, (v >>> 16) & 0xff, (v >>> 8) & 0xff, v & 0xff);\n}\nexport default parse;\n", "import validate from './validate.js';\nconst byteToHex = [];\nfor (let i = 0; i < 256; ++i) {\n    byteToHex.push((i + 0x100).toString(16).slice(1));\n}\nexport function unsafeStringify(arr, offset = 0) {\n    return (byteToHex[arr[offset + 0]] +\n        byteToHex[arr[offset + 1]] +\n        byteToHex[arr[offset + 2]] +\n        byteToHex[arr[offset + 3]] +\n        '-' +\n        byteToHex[arr[offset + 4]] +\n        byteToHex[arr[offset + 5]] +\n        '-' +\n        byteToHex[arr[offset + 6]] +\n        byteToHex[arr[offset + 7]] +\n        '-' +\n        byteToHex[arr[offset + 8]] +\n        byteToHex[arr[offset + 9]] +\n        '-' +\n        byteToHex[arr[offset + 10]] +\n        byteToHex[arr[offset + 11]] +\n        byteToHex[arr[offset + 12]] +\n        byteToHex[arr[offset + 13]] +\n        byteToHex[arr[offset + 14]] +\n        byteToHex[arr[offset + 15]]).toLowerCase();\n}\nfunction stringify(arr, offset = 0) {\n    const uuid = unsafeStringify(arr, offset);\n    if (!validate(uuid)) {\n        throw TypeError('Stringified UUID is invalid');\n    }\n    return uuid;\n}\nexport default stringify;\n", "let getRandomValues;\nconst rnds8 = new Uint8Array(16);\nexport default function rng() {\n    if (!getRandomValues) {\n        if (typeof crypto === 'undefined' || !crypto.getRandomValues) {\n            throw new Error('crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported');\n        }\n        getRandomValues = crypto.getRandomValues.bind(crypto);\n    }\n    return getRandomValues(rnds8);\n}\n", "import rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\nconst _state = {};\nfunction v1(options, buf, offset) {\n    let bytes;\n    const isV6 = options?._v6 ?? false;\n    if (options) {\n        const optionsKeys = Object.keys(options);\n        if (optionsKeys.length === 1 && optionsKeys[0] === '_v6') {\n            options = undefined;\n        }\n    }\n    if (options) {\n        bytes = v1Bytes(options.random ?? options.rng?.() ?? rng(), options.msecs, options.nsecs, options.clockseq, options.node, buf, offset);\n    }\n    else {\n        const now = Date.now();\n        const rnds = rng();\n        updateV1State(_state, now, rnds);\n        bytes = v1Bytes(rnds, _state.msecs, _state.nsecs, isV6 ? undefined : _state.clockseq, isV6 ? undefined : _state.node, buf, offset);\n    }\n    return buf ?? unsafeStringify(bytes);\n}\nexport function updateV1State(state, now, rnds) {\n    state.msecs ??= -Infinity;\n    state.nsecs ??= 0;\n    if (now === state.msecs) {\n        state.nsecs++;\n        if (state.nsecs >= 10000) {\n            state.node = undefined;\n            state.nsecs = 0;\n        }\n    }\n    else if (now > state.msecs) {\n        state.nsecs = 0;\n    }\n    else if (now < state.msecs) {\n        state.node = undefined;\n    }\n    if (!state.node) {\n        state.node = rnds.slice(10, 16);\n        state.node[0] |= 0x01;\n        state.clockseq = ((rnds[8] << 8) | rnds[9]) & 0x3fff;\n    }\n    state.msecs = now;\n    return state;\n}\nfunction v1Bytes(rnds, msecs, nsecs, clockseq, node, buf, offset = 0) {\n    if (rnds.length < 16) {\n        throw new Error('Random bytes length must be >= 16');\n    }\n    if (!buf) {\n        buf = new Uint8Array(16);\n        offset = 0;\n    }\n    else {\n        if (offset < 0 || offset + 16 > buf.length) {\n            throw new RangeError(`UUID byte range ${offset}:${offset + 15} is out of buffer bounds`);\n        }\n    }\n    msecs ??= Date.now();\n    nsecs ??= 0;\n    clockseq ??= ((rnds[8] << 8) | rnds[9]) & 0x3fff;\n    node ??= rnds.slice(10, 16);\n    msecs += 12219292800000;\n    const tl = ((msecs & 0xfffffff) * 10000 + nsecs) % 0x100000000;\n    buf[offset++] = (tl >>> 24) & 0xff;\n    buf[offset++] = (tl >>> 16) & 0xff;\n    buf[offset++] = (tl >>> 8) & 0xff;\n    buf[offset++] = tl & 0xff;\n    const tmh = ((msecs / 0x100000000) * 10000) & 0xfffffff;\n    buf[offset++] = (tmh >>> 8) & 0xff;\n    buf[offset++] = tmh & 0xff;\n    buf[offset++] = ((tmh >>> 24) & 0xf) | 0x10;\n    buf[offset++] = (tmh >>> 16) & 0xff;\n    buf[offset++] = (clockseq >>> 8) | 0x80;\n    buf[offset++] = clockseq & 0xff;\n    for (let n = 0; n < 6; ++n) {\n        buf[offset++] = node[n];\n    }\n    return buf;\n}\nexport default v1;\n", "import parse from './parse.js';\nimport { unsafeStringify } from './stringify.js';\nexport default function v1ToV6(uuid) {\n    const v1Bytes = typeof uuid === 'string' ? parse(uuid) : uuid;\n    const v6Bytes = _v1ToV6(v1Bytes);\n    return typeof uuid === 'string' ? unsafeStringify(v6Bytes) : v6Bytes;\n}\nfunction _v1ToV6(v1Bytes) {\n    return Uint8Array.of(((v1Bytes[6] & 0x0f) << 4) | ((v1Bytes[7] >> 4) & 0x0f), ((v1Bytes[7] & 0x0f) << 4) | ((v1Bytes[4] & 0xf0) >> 4), ((v1Bytes[4] & 0x0f) << 4) | ((v1Bytes[5] & 0xf0) >> 4), ((v1Bytes[5] & 0x0f) << 4) | ((v1Bytes[0] & 0xf0) >> 4), ((v1Bytes[0] & 0x0f) << 4) | ((v1Bytes[1] & 0xf0) >> 4), ((v1Bytes[1] & 0x0f) << 4) | ((v1Bytes[2] & 0xf0) >> 4), 0x60 | (v1Bytes[2] & 0x0f), v1Bytes[3], v1Bytes[8], v1Bytes[9], v1Bytes[10], v1Bytes[11], v1Bytes[12], v1Bytes[13], v1Bytes[14], v1Bytes[15]);\n}\n", "function md5(bytes) {\n    const words = uint8ToUint32(bytes);\n    const md5Bytes = wordsToMd5(words, bytes.length * 8);\n    return uint32ToUint8(md5Bytes);\n}\nfunction uint32ToUint8(input) {\n    const bytes = new Uint8Array(input.length * 4);\n    for (let i = 0; i < input.length * 4; i++) {\n        bytes[i] = (input[i >> 2] >>> ((i % 4) * 8)) & 0xff;\n    }\n    return bytes;\n}\nfunction getOutputLength(inputLength8) {\n    return (((inputLength8 + 64) >>> 9) << 4) + 14 + 1;\n}\nfunction wordsToMd5(x, len) {\n    const xpad = new Uint32Array(getOutputLength(len)).fill(0);\n    xpad.set(x);\n    xpad[len >> 5] |= 0x80 << len % 32;\n    xpad[xpad.length - 1] = len;\n    x = xpad;\n    let a = 1732584193;\n    let b = -271733879;\n    let c = -1732584194;\n    let d = 271733878;\n    for (let i = 0; i < x.length; i += 16) {\n        const olda = a;\n        const oldb = b;\n        const oldc = c;\n        const oldd = d;\n        a = md5ff(a, b, c, d, x[i], 7, -680876936);\n        d = md5ff(d, a, b, c, x[i + 1], 12, -389564586);\n        c = md5ff(c, d, a, b, x[i + 2], 17, 606105819);\n        b = md5ff(b, c, d, a, x[i + 3], 22, -1044525330);\n        a = md5ff(a, b, c, d, x[i + 4], 7, -176418897);\n        d = md5ff(d, a, b, c, x[i + 5], 12, 1200080426);\n        c = md5ff(c, d, a, b, x[i + 6], 17, -1473231341);\n        b = md5ff(b, c, d, a, x[i + 7], 22, -45705983);\n        a = md5ff(a, b, c, d, x[i + 8], 7, 1770035416);\n        d = md5ff(d, a, b, c, x[i + 9], 12, -1958414417);\n        c = md5ff(c, d, a, b, x[i + 10], 17, -42063);\n        b = md5ff(b, c, d, a, x[i + 11], 22, -1990404162);\n        a = md5ff(a, b, c, d, x[i + 12], 7, 1804603682);\n        d = md5ff(d, a, b, c, x[i + 13], 12, -40341101);\n        c = md5ff(c, d, a, b, x[i + 14], 17, -1502002290);\n        b = md5ff(b, c, d, a, x[i + 15], 22, 1236535329);\n        a = md5gg(a, b, c, d, x[i + 1], 5, -165796510);\n        d = md5gg(d, a, b, c, x[i + 6], 9, -1069501632);\n        c = md5gg(c, d, a, b, x[i + 11], 14, 643717713);\n        b = md5gg(b, c, d, a, x[i], 20, -373897302);\n        a = md5gg(a, b, c, d, x[i + 5], 5, -701558691);\n        d = md5gg(d, a, b, c, x[i + 10], 9, 38016083);\n        c = md5gg(c, d, a, b, x[i + 15], 14, -660478335);\n        b = md5gg(b, c, d, a, x[i + 4], 20, -405537848);\n        a = md5gg(a, b, c, d, x[i + 9], 5, 568446438);\n        d = md5gg(d, a, b, c, x[i + 14], 9, -1019803690);\n        c = md5gg(c, d, a, b, x[i + 3], 14, -187363961);\n        b = md5gg(b, c, d, a, x[i + 8], 20, 1163531501);\n        a = md5gg(a, b, c, d, x[i + 13], 5, -1444681467);\n        d = md5gg(d, a, b, c, x[i + 2], 9, -51403784);\n        c = md5gg(c, d, a, b, x[i + 7], 14, 1735328473);\n        b = md5gg(b, c, d, a, x[i + 12], 20, -1926607734);\n        a = md5hh(a, b, c, d, x[i + 5], 4, -378558);\n        d = md5hh(d, a, b, c, x[i + 8], 11, -2022574463);\n        c = md5hh(c, d, a, b, x[i + 11], 16, 1839030562);\n        b = md5hh(b, c, d, a, x[i + 14], 23, -35309556);\n        a = md5hh(a, b, c, d, x[i + 1], 4, -1530992060);\n        d = md5hh(d, a, b, c, x[i + 4], 11, 1272893353);\n        c = md5hh(c, d, a, b, x[i + 7], 16, -155497632);\n        b = md5hh(b, c, d, a, x[i + 10], 23, -1094730640);\n        a = md5hh(a, b, c, d, x[i + 13], 4, 681279174);\n        d = md5hh(d, a, b, c, x[i], 11, -358537222);\n        c = md5hh(c, d, a, b, x[i + 3], 16, -722521979);\n        b = md5hh(b, c, d, a, x[i + 6], 23, 76029189);\n        a = md5hh(a, b, c, d, x[i + 9], 4, -640364487);\n        d = md5hh(d, a, b, c, x[i + 12], 11, -421815835);\n        c = md5hh(c, d, a, b, x[i + 15], 16, 530742520);\n        b = md5hh(b, c, d, a, x[i + 2], 23, -995338651);\n        a = md5ii(a, b, c, d, x[i], 6, -198630844);\n        d = md5ii(d, a, b, c, x[i + 7], 10, 1126891415);\n        c = md5ii(c, d, a, b, x[i + 14], 15, -1416354905);\n        b = md5ii(b, c, d, a, x[i + 5], 21, -57434055);\n        a = md5ii(a, b, c, d, x[i + 12], 6, 1700485571);\n        d = md5ii(d, a, b, c, x[i + 3], 10, -1894986606);\n        c = md5ii(c, d, a, b, x[i + 10], 15, -1051523);\n        b = md5ii(b, c, d, a, x[i + 1], 21, -2054922799);\n        a = md5ii(a, b, c, d, x[i + 8], 6, 1873313359);\n        d = md5ii(d, a, b, c, x[i + 15], 10, -30611744);\n        c = md5ii(c, d, a, b, x[i + 6], 15, -1560198380);\n        b = md5ii(b, c, d, a, x[i + 13], 21, 1309151649);\n        a = md5ii(a, b, c, d, x[i + 4], 6, -145523070);\n        d = md5ii(d, a, b, c, x[i + 11], 10, -1120210379);\n        c = md5ii(c, d, a, b, x[i + 2], 15, 718787259);\n        b = md5ii(b, c, d, a, x[i + 9], 21, -343485551);\n        a = safeAdd(a, olda);\n        b = safeAdd(b, oldb);\n        c = safeAdd(c, oldc);\n        d = safeAdd(d, oldd);\n    }\n    return Uint32Array.of(a, b, c, d);\n}\nfunction uint8ToUint32(input) {\n    if (input.length === 0) {\n        return new Uint32Array();\n    }\n    const output = new Uint32Array(getOutputLength(input.length * 8)).fill(0);\n    for (let i = 0; i < input.length; i++) {\n        output[i >> 2] |= (input[i] & 0xff) << ((i % 4) * 8);\n    }\n    return output;\n}\nfunction safeAdd(x, y) {\n    const lsw = (x & 0xffff) + (y & 0xffff);\n    const msw = (x >> 16) + (y >> 16) + (lsw >> 16);\n    return (msw << 16) | (lsw & 0xffff);\n}\nfunction bitRotateLeft(num, cnt) {\n    return (num << cnt) | (num >>> (32 - cnt));\n}\nfunction md5cmn(q, a, b, x, s, t) {\n    return safeAdd(bitRotateLeft(safeAdd(safeAdd(a, q), safeAdd(x, t)), s), b);\n}\nfunction md5ff(a, b, c, d, x, s, t) {\n    return md5cmn((b & c) | (~b & d), a, b, x, s, t);\n}\nfunction md5gg(a, b, c, d, x, s, t) {\n    return md5cmn((b & d) | (c & ~d), a, b, x, s, t);\n}\nfunction md5hh(a, b, c, d, x, s, t) {\n    return md5cmn(b ^ c ^ d, a, b, x, s, t);\n}\nfunction md5ii(a, b, c, d, x, s, t) {\n    return md5cmn(c ^ (b | ~d), a, b, x, s, t);\n}\nexport default md5;\n", "import parse from './parse.js';\nimport { unsafeStringify } from './stringify.js';\nexport function stringToBytes(str) {\n    str = unescape(encodeURIComponent(str));\n    const bytes = new Uint8Array(str.length);\n    for (let i = 0; i < str.length; ++i) {\n        bytes[i] = str.charCodeAt(i);\n    }\n    return bytes;\n}\nexport const DNS = '6ba7b810-9dad-11d1-80b4-00c04fd430c8';\nexport const URL = '6ba7b811-9dad-11d1-80b4-00c04fd430c8';\nexport default function v35(version, hash, value, namespace, buf, offset) {\n    const valueBytes = typeof value === 'string' ? stringToBytes(value) : value;\n    const namespaceBytes = typeof namespace === 'string' ? parse(namespace) : namespace;\n    if (typeof namespace === 'string') {\n        namespace = parse(namespace);\n    }\n    if (namespace?.length !== 16) {\n        throw TypeError('Namespace must be array-like (16 iterable integer values, 0-255)');\n    }\n    let bytes = new Uint8Array(16 + valueBytes.length);\n    bytes.set(namespaceBytes);\n    bytes.set(valueBytes, namespaceBytes.length);\n    bytes = hash(bytes);\n    bytes[6] = (bytes[6] & 0x0f) | version;\n    bytes[8] = (bytes[8] & 0x3f) | 0x80;\n    if (buf) {\n        offset = offset || 0;\n        for (let i = 0; i < 16; ++i) {\n            buf[offset + i] = bytes[i];\n        }\n        return buf;\n    }\n    return unsafeStringify(bytes);\n}\n", "import md5 from './md5.js';\nimport v35, { DNS, URL } from './v35.js';\nexport { DNS, URL } from './v35.js';\nfunction v3(value, namespace, buf, offset) {\n    return v35(0x30, md5, value, namespace, buf, offset);\n}\nv3.DNS = DNS;\nv3.URL = URL;\nexport default v3;\n", "const randomUUID = typeof crypto !== 'undefined' && crypto.randomUUID && crypto.randomUUID.bind(crypto);\nexport default { randomUUID };\n", "import native from './native.js';\nimport rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\nfunction v4(options, buf, offset) {\n    if (native.randomUUID && !buf && !options) {\n        return native.randomUUID();\n    }\n    options = options || {};\n    const rnds = options.random ?? options.rng?.() ?? rng();\n    if (rnds.length < 16) {\n        throw new Error('Random bytes length must be >= 16');\n    }\n    rnds[6] = (rnds[6] & 0x0f) | 0x40;\n    rnds[8] = (rnds[8] & 0x3f) | 0x80;\n    if (buf) {\n        offset = offset || 0;\n        if (offset < 0 || offset + 16 > buf.length) {\n            throw new RangeError(`UUID byte range ${offset}:${offset + 15} is out of buffer bounds`);\n        }\n        for (let i = 0; i < 16; ++i) {\n            buf[offset + i] = rnds[i];\n        }\n        return buf;\n    }\n    return unsafeStringify(rnds);\n}\nexport default v4;\n", "function f(s, x, y, z) {\n    switch (s) {\n        case 0:\n            return (x & y) ^ (~x & z);\n        case 1:\n            return x ^ y ^ z;\n        case 2:\n            return (x & y) ^ (x & z) ^ (y & z);\n        case 3:\n            return x ^ y ^ z;\n    }\n}\nfunction ROTL(x, n) {\n    return (x << n) | (x >>> (32 - n));\n}\nfunction sha1(bytes) {\n    const K = [0x5a827999, 0x6ed9eba1, 0x8f1bbcdc, 0xca62c1d6];\n    const H = [0x67452301, 0xefcdab89, 0x98badcfe, 0x10325476, 0xc3d2e1f0];\n    const newBytes = new Uint8Array(bytes.length + 1);\n    newBytes.set(bytes);\n    newBytes[bytes.length] = 0x80;\n    bytes = newBytes;\n    const l = bytes.length / 4 + 2;\n    const N = Math.ceil(l / 16);\n    const M = new Array(N);\n    for (let i = 0; i < N; ++i) {\n        const arr = new Uint32Array(16);\n        for (let j = 0; j < 16; ++j) {\n            arr[j] =\n                (bytes[i * 64 + j * 4] << 24) |\n                    (bytes[i * 64 + j * 4 + 1] << 16) |\n                    (bytes[i * 64 + j * 4 + 2] << 8) |\n                    bytes[i * 64 + j * 4 + 3];\n        }\n        M[i] = arr;\n    }\n    M[N - 1][14] = ((bytes.length - 1) * 8) / Math.pow(2, 32);\n    M[N - 1][14] = Math.floor(M[N - 1][14]);\n    M[N - 1][15] = ((bytes.length - 1) * 8) & 0xffffffff;\n    for (let i = 0; i < N; ++i) {\n        const W = new Uint32Array(80);\n        for (let t = 0; t < 16; ++t) {\n            W[t] = M[i][t];\n        }\n        for (let t = 16; t < 80; ++t) {\n            W[t] = ROTL(W[t - 3] ^ W[t - 8] ^ W[t - 14] ^ W[t - 16], 1);\n        }\n        let a = H[0];\n        let b = H[1];\n        let c = H[2];\n        let d = H[3];\n        let e = H[4];\n        for (let t = 0; t < 80; ++t) {\n            const s = Math.floor(t / 20);\n            const T = (ROTL(a, 5) + f(s, b, c, d) + e + K[s] + W[t]) >>> 0;\n            e = d;\n            d = c;\n            c = ROTL(b, 30) >>> 0;\n            b = a;\n            a = T;\n        }\n        H[0] = (H[0] + a) >>> 0;\n        H[1] = (H[1] + b) >>> 0;\n        H[2] = (H[2] + c) >>> 0;\n        H[3] = (H[3] + d) >>> 0;\n        H[4] = (H[4] + e) >>> 0;\n    }\n    return Uint8Array.of(H[0] >> 24, H[0] >> 16, H[0] >> 8, H[0], H[1] >> 24, H[1] >> 16, H[1] >> 8, H[1], H[2] >> 24, H[2] >> 16, H[2] >> 8, H[2], H[3] >> 24, H[3] >> 16, H[3] >> 8, H[3], H[4] >> 24, H[4] >> 16, H[4] >> 8, H[4]);\n}\nexport default sha1;\n", "import sha1 from './sha1.js';\nimport v35, { DNS, URL } from './v35.js';\nexport { DNS, URL } from './v35.js';\nfunction v5(value, namespace, buf, offset) {\n    return v35(0x50, sha1, value, namespace, buf, offset);\n}\nv5.DNS = DNS;\nv5.URL = URL;\nexport default v5;\n", "import { unsafeStringify } from './stringify.js';\nimport v1 from './v1.js';\nimport v1ToV6 from './v1ToV6.js';\nfunction v6(options, buf, offset) {\n    options ??= {};\n    offset ??= 0;\n    let bytes = v1({ ...options, _v6: true }, new Uint8Array(16));\n    bytes = v1ToV6(bytes);\n    if (buf) {\n        for (let i = 0; i < 16; i++) {\n            buf[offset + i] = bytes[i];\n        }\n        return buf;\n    }\n    return unsafeStringify(bytes);\n}\nexport default v6;\n", "import parse from './parse.js';\nimport { unsafeStringify } from './stringify.js';\nexport default function v6ToV1(uuid) {\n    const v6Bytes = typeof uuid === 'string' ? parse(uuid) : uuid;\n    const v1Bytes = _v6ToV1(v6Bytes);\n    return typeof uuid === 'string' ? unsafeStringify(v1Bytes) : v1Bytes;\n}\nfunction _v6ToV1(v6Bytes) {\n    return Uint8Array.of(((v6Bytes[3] & 0x0f) << 4) | ((v6Bytes[4] >> 4) & 0x0f), ((v6Bytes[4] & 0x0f) << 4) | ((v6Bytes[5] & 0xf0) >> 4), ((v6Bytes[5] & 0x0f) << 4) | (v6Bytes[6] & 0x0f), v6Bytes[7], ((v6Bytes[1] & 0x0f) << 4) | ((v6Bytes[2] & 0xf0) >> 4), ((v6Bytes[2] & 0x0f) << 4) | ((v6Bytes[3] & 0xf0) >> 4), 0x10 | ((v6Bytes[0] & 0xf0) >> 4), ((v6Bytes[0] & 0x0f) << 4) | ((v6Bytes[1] & 0xf0) >> 4), v6Bytes[8], v6Bytes[9], v6Bytes[10], v6Bytes[11], v6Bytes[12], v6Bytes[13], v6Bytes[14], v6Bytes[15]);\n}\n", "import rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\nconst _state = {};\nfunction v7(options, buf, offset) {\n    let bytes;\n    if (options) {\n        bytes = v7Bytes(options.random ?? options.rng?.() ?? rng(), options.msecs, options.seq, buf, offset);\n    }\n    else {\n        const now = Date.now();\n        const rnds = rng();\n        updateV7State(_state, now, rnds);\n        bytes = v7Bytes(rnds, _state.msecs, _state.seq, buf, offset);\n    }\n    return buf ?? unsafeStringify(bytes);\n}\nexport function updateV7State(state, now, rnds) {\n    state.msecs ??= -Infinity;\n    state.seq ??= 0;\n    if (now > state.msecs) {\n        state.seq = (rnds[6] << 23) | (rnds[7] << 16) | (rnds[8] << 8) | rnds[9];\n        state.msecs = now;\n    }\n    else {\n        state.seq = (state.seq + 1) | 0;\n        if (state.seq === 0) {\n            state.msecs++;\n        }\n    }\n    return state;\n}\nfunction v7Bytes(rnds, msecs, seq, buf, offset = 0) {\n    if (rnds.length < 16) {\n        throw new Error('Random bytes length must be >= 16');\n    }\n    if (!buf) {\n        buf = new Uint8Array(16);\n        offset = 0;\n    }\n    else {\n        if (offset < 0 || offset + 16 > buf.length) {\n            throw new RangeError(`UUID byte range ${offset}:${offset + 15} is out of buffer bounds`);\n        }\n    }\n    msecs ??= Date.now();\n    seq ??= ((rnds[6] * 0x7f) << 24) | (rnds[7] << 16) | (rnds[8] << 8) | rnds[9];\n    buf[offset++] = (msecs / 0x10000000000) & 0xff;\n    buf[offset++] = (msecs / 0x100000000) & 0xff;\n    buf[offset++] = (msecs / 0x1000000) & 0xff;\n    buf[offset++] = (msecs / 0x10000) & 0xff;\n    buf[offset++] = (msecs / 0x100) & 0xff;\n    buf[offset++] = msecs & 0xff;\n    buf[offset++] = 0x70 | ((seq >>> 28) & 0x0f);\n    buf[offset++] = (seq >>> 20) & 0xff;\n    buf[offset++] = 0x80 | ((seq >>> 14) & 0x3f);\n    buf[offset++] = (seq >>> 6) & 0xff;\n    buf[offset++] = ((seq << 2) & 0xff) | (rnds[10] & 0x03);\n    buf[offset++] = rnds[11];\n    buf[offset++] = rnds[12];\n    buf[offset++] = rnds[13];\n    buf[offset++] = rnds[14];\n    buf[offset++] = rnds[15];\n    return buf;\n}\nexport default v7;\n", "import validate from './validate.js';\nfunction version(uuid) {\n    if (!validate(uuid)) {\n        throw TypeError('Invalid UUID');\n    }\n    return parseInt(uuid.slice(14, 15), 16);\n}\nexport default version;\n"], "mappings": ";;;AAAA,IAAO,cAAQ;;;ACAf,IAAO,cAAQ;;;ACAf,IAAO,gBAAQ;;;ACCf,SAAS,SAAS,MAAM;AACpB,SAAO,OAAO,SAAS,YAAY,cAAM,KAAK,IAAI;AACtD;AACA,IAAO,mBAAQ;;;ACHf,SAAS,MAAM,MAAM;AACjB,MAAI,CAAC,iBAAS,IAAI,GAAG;AACjB,UAAM,UAAU,cAAc;AAAA,EAClC;AACA,MAAI;AACJ,SAAO,WAAW,IAAI,IAAI,SAAS,KAAK,MAAM,GAAG,CAAC,GAAG,EAAE,OAAO,IAAK,MAAM,KAAM,KAAO,MAAM,IAAK,KAAM,IAAI,MAAO,IAAI,SAAS,KAAK,MAAM,GAAG,EAAE,GAAG,EAAE,OAAO,GAAG,IAAI,MAAO,IAAI,SAAS,KAAK,MAAM,IAAI,EAAE,GAAG,EAAE,OAAO,GAAG,IAAI,MAAO,IAAI,SAAS,KAAK,MAAM,IAAI,EAAE,GAAG,EAAE,OAAO,GAAG,IAAI,MAAQ,IAAI,SAAS,KAAK,MAAM,IAAI,EAAE,GAAG,EAAE,KAAK,gBAAiB,KAAO,IAAI,aAAe,KAAO,MAAM,KAAM,KAAO,MAAM,KAAM,KAAO,MAAM,IAAK,KAAM,IAAI,GAAI;AACvb;AACA,IAAO,gBAAQ;;;ACPf,IAAM,YAAY,CAAC;AACnB,SAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC1B,YAAU,MAAM,IAAI,KAAO,SAAS,EAAE,EAAE,MAAM,CAAC,CAAC;AACpD;AACO,SAAS,gBAAgB,KAAK,SAAS,GAAG;AAC7C,UAAQ,UAAU,IAAI,SAAS,CAAC,CAAC,IAC7B,UAAU,IAAI,SAAS,CAAC,CAAC,IACzB,UAAU,IAAI,SAAS,CAAC,CAAC,IACzB,UAAU,IAAI,SAAS,CAAC,CAAC,IACzB,MACA,UAAU,IAAI,SAAS,CAAC,CAAC,IACzB,UAAU,IAAI,SAAS,CAAC,CAAC,IACzB,MACA,UAAU,IAAI,SAAS,CAAC,CAAC,IACzB,UAAU,IAAI,SAAS,CAAC,CAAC,IACzB,MACA,UAAU,IAAI,SAAS,CAAC,CAAC,IACzB,UAAU,IAAI,SAAS,CAAC,CAAC,IACzB,MACA,UAAU,IAAI,SAAS,EAAE,CAAC,IAC1B,UAAU,IAAI,SAAS,EAAE,CAAC,IAC1B,UAAU,IAAI,SAAS,EAAE,CAAC,IAC1B,UAAU,IAAI,SAAS,EAAE,CAAC,IAC1B,UAAU,IAAI,SAAS,EAAE,CAAC,IAC1B,UAAU,IAAI,SAAS,EAAE,CAAC,GAAG,YAAY;AACjD;AACA,SAAS,UAAU,KAAK,SAAS,GAAG;AAChC,QAAM,OAAO,gBAAgB,KAAK,MAAM;AACxC,MAAI,CAAC,iBAAS,IAAI,GAAG;AACjB,UAAM,UAAU,6BAA6B;AAAA,EACjD;AACA,SAAO;AACX;AACA,IAAO,oBAAQ;;;AClCf,IAAI;AACJ,IAAM,QAAQ,IAAI,WAAW,EAAE;AAChB,SAAR,MAAuB;AAC1B,MAAI,CAAC,iBAAiB;AAClB,QAAI,OAAO,WAAW,eAAe,CAAC,OAAO,iBAAiB;AAC1D,YAAM,IAAI,MAAM,0GAA0G;AAAA,IAC9H;AACA,sBAAkB,OAAO,gBAAgB,KAAK,MAAM;AAAA,EACxD;AACA,SAAO,gBAAgB,KAAK;AAChC;;;ACRA,IAAM,SAAS,CAAC;AAChB,SAAS,GAAG,SAAS,KAAK,QAAQ;AAHlC;AAII,MAAI;AACJ,QAAM,QAAO,mCAAS,QAAO;AAC7B,MAAI,SAAS;AACT,UAAM,cAAc,OAAO,KAAK,OAAO;AACvC,QAAI,YAAY,WAAW,KAAK,YAAY,CAAC,MAAM,OAAO;AACtD,gBAAU;AAAA,IACd;AAAA,EACJ;AACA,MAAI,SAAS;AACT,YAAQ,QAAQ,QAAQ,YAAU,aAAQ,QAAR,qCAAmB,IAAI,GAAG,QAAQ,OAAO,QAAQ,OAAO,QAAQ,UAAU,QAAQ,MAAM,KAAK,MAAM;AAAA,EACzI,OACK;AACD,UAAM,MAAM,KAAK,IAAI;AACrB,UAAM,OAAO,IAAI;AACjB,kBAAc,QAAQ,KAAK,IAAI;AAC/B,YAAQ,QAAQ,MAAM,OAAO,OAAO,OAAO,OAAO,OAAO,SAAY,OAAO,UAAU,OAAO,SAAY,OAAO,MAAM,KAAK,MAAM;AAAA,EACrI;AACA,SAAO,OAAO,gBAAgB,KAAK;AACvC;AACO,SAAS,cAAc,OAAO,KAAK,MAAM;AAC5C,QAAM,UAAN,MAAM,QAAU;AAChB,QAAM,UAAN,MAAM,QAAU;AAChB,MAAI,QAAQ,MAAM,OAAO;AACrB,UAAM;AACN,QAAI,MAAM,SAAS,KAAO;AACtB,YAAM,OAAO;AACb,YAAM,QAAQ;AAAA,IAClB;AAAA,EACJ,WACS,MAAM,MAAM,OAAO;AACxB,UAAM,QAAQ;AAAA,EAClB,WACS,MAAM,MAAM,OAAO;AACxB,UAAM,OAAO;AAAA,EACjB;AACA,MAAI,CAAC,MAAM,MAAM;AACb,UAAM,OAAO,KAAK,MAAM,IAAI,EAAE;AAC9B,UAAM,KAAK,CAAC,KAAK;AACjB,UAAM,YAAa,KAAK,CAAC,KAAK,IAAK,KAAK,CAAC,KAAK;AAAA,EAClD;AACA,QAAM,QAAQ;AACd,SAAO;AACX;AACA,SAAS,QAAQ,MAAM,OAAO,OAAO,UAAU,MAAM,KAAK,SAAS,GAAG;AAClE,MAAI,KAAK,SAAS,IAAI;AAClB,UAAM,IAAI,MAAM,mCAAmC;AAAA,EACvD;AACA,MAAI,CAAC,KAAK;AACN,UAAM,IAAI,WAAW,EAAE;AACvB,aAAS;AAAA,EACb,OACK;AACD,QAAI,SAAS,KAAK,SAAS,KAAK,IAAI,QAAQ;AACxC,YAAM,IAAI,WAAW,mBAAmB,MAAM,IAAI,SAAS,EAAE,0BAA0B;AAAA,IAC3F;AAAA,EACJ;AACA,oBAAU,KAAK,IAAI;AACnB,oBAAU;AACV,2BAAe,KAAK,CAAC,KAAK,IAAK,KAAK,CAAC,KAAK;AAC1C,kBAAS,KAAK,MAAM,IAAI,EAAE;AAC1B,WAAS;AACT,QAAM,OAAO,QAAQ,aAAa,MAAQ,SAAS;AACnD,MAAI,QAAQ,IAAK,OAAO,KAAM;AAC9B,MAAI,QAAQ,IAAK,OAAO,KAAM;AAC9B,MAAI,QAAQ,IAAK,OAAO,IAAK;AAC7B,MAAI,QAAQ,IAAI,KAAK;AACrB,QAAM,MAAQ,QAAQ,aAAe,MAAS;AAC9C,MAAI,QAAQ,IAAK,QAAQ,IAAK;AAC9B,MAAI,QAAQ,IAAI,MAAM;AACtB,MAAI,QAAQ,IAAM,QAAQ,KAAM,KAAO;AACvC,MAAI,QAAQ,IAAK,QAAQ,KAAM;AAC/B,MAAI,QAAQ,IAAK,aAAa,IAAK;AACnC,MAAI,QAAQ,IAAI,WAAW;AAC3B,WAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACxB,QAAI,QAAQ,IAAI,KAAK,CAAC;AAAA,EAC1B;AACA,SAAO;AACX;AACA,IAAO,aAAQ;;;AChFA,SAAR,OAAwB,MAAM;AACjC,QAAMA,WAAU,OAAO,SAAS,WAAW,cAAM,IAAI,IAAI;AACzD,QAAM,UAAU,QAAQA,QAAO;AAC/B,SAAO,OAAO,SAAS,WAAW,gBAAgB,OAAO,IAAI;AACjE;AACA,SAAS,QAAQA,UAAS;AACtB,SAAO,WAAW,IAAKA,SAAQ,CAAC,IAAI,OAAS,IAAOA,SAAQ,CAAC,KAAK,IAAK,KAASA,SAAQ,CAAC,IAAI,OAAS,KAAOA,SAAQ,CAAC,IAAI,QAAS,IAAMA,SAAQ,CAAC,IAAI,OAAS,KAAOA,SAAQ,CAAC,IAAI,QAAS,IAAMA,SAAQ,CAAC,IAAI,OAAS,KAAOA,SAAQ,CAAC,IAAI,QAAS,IAAMA,SAAQ,CAAC,IAAI,OAAS,KAAOA,SAAQ,CAAC,IAAI,QAAS,IAAMA,SAAQ,CAAC,IAAI,OAAS,KAAOA,SAAQ,CAAC,IAAI,QAAS,GAAI,KAAQA,SAAQ,CAAC,IAAI,IAAOA,SAAQ,CAAC,GAAGA,SAAQ,CAAC,GAAGA,SAAQ,CAAC,GAAGA,SAAQ,EAAE,GAAGA,SAAQ,EAAE,GAAGA,SAAQ,EAAE,GAAGA,SAAQ,EAAE,GAAGA,SAAQ,EAAE,GAAGA,SAAQ,EAAE,CAAC;AAC3f;;;ACTA,SAAS,IAAI,OAAO;AAChB,QAAM,QAAQ,cAAc,KAAK;AACjC,QAAM,WAAW,WAAW,OAAO,MAAM,SAAS,CAAC;AACnD,SAAO,cAAc,QAAQ;AACjC;AACA,SAAS,cAAc,OAAO;AAC1B,QAAM,QAAQ,IAAI,WAAW,MAAM,SAAS,CAAC;AAC7C,WAAS,IAAI,GAAG,IAAI,MAAM,SAAS,GAAG,KAAK;AACvC,UAAM,CAAC,IAAK,MAAM,KAAK,CAAC,MAAQ,IAAI,IAAK,IAAM;AAAA,EACnD;AACA,SAAO;AACX;AACA,SAAS,gBAAgB,cAAc;AACnC,UAAU,eAAe,OAAQ,KAAM,KAAK,KAAK;AACrD;AACA,SAAS,WAAW,GAAG,KAAK;AACxB,QAAM,OAAO,IAAI,YAAY,gBAAgB,GAAG,CAAC,EAAE,KAAK,CAAC;AACzD,OAAK,IAAI,CAAC;AACV,OAAK,OAAO,CAAC,KAAK,OAAQ,MAAM;AAChC,OAAK,KAAK,SAAS,CAAC,IAAI;AACxB,MAAI;AACJ,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACR,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK,IAAI;AACnC,UAAM,OAAO;AACb,UAAM,OAAO;AACb,UAAM,OAAO;AACb,UAAM,OAAO;AACb,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,UAAU;AACzC,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,MAAM;AAC3C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,WAAW;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,UAAU;AAC1C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,QAAQ;AAC5C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,SAAS;AAC5C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,SAAS;AAC5C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,OAAO;AAC1C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,WAAW;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,SAAS;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,UAAU;AAC1C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,QAAQ;AAC5C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,UAAU;AACzC,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,GAAG,UAAU;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,QAAQ;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,SAAS;AAC9C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,WAAW;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,UAAU;AAC/C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,GAAG,UAAU;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,IAAI,WAAW;AAChD,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,SAAS;AAC7C,QAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,UAAU;AAC9C,QAAI,QAAQ,GAAG,IAAI;AACnB,QAAI,QAAQ,GAAG,IAAI;AACnB,QAAI,QAAQ,GAAG,IAAI;AACnB,QAAI,QAAQ,GAAG,IAAI;AAAA,EACvB;AACA,SAAO,YAAY,GAAG,GAAG,GAAG,GAAG,CAAC;AACpC;AACA,SAAS,cAAc,OAAO;AAC1B,MAAI,MAAM,WAAW,GAAG;AACpB,WAAO,IAAI,YAAY;AAAA,EAC3B;AACA,QAAM,SAAS,IAAI,YAAY,gBAAgB,MAAM,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC;AACxE,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,WAAO,KAAK,CAAC,MAAM,MAAM,CAAC,IAAI,QAAW,IAAI,IAAK;AAAA,EACtD;AACA,SAAO;AACX;AACA,SAAS,QAAQ,GAAG,GAAG;AACnB,QAAM,OAAO,IAAI,UAAW,IAAI;AAChC,QAAM,OAAO,KAAK,OAAO,KAAK,OAAO,OAAO;AAC5C,SAAQ,OAAO,KAAO,MAAM;AAChC;AACA,SAAS,cAAc,KAAK,KAAK;AAC7B,SAAQ,OAAO,MAAQ,QAAS,KAAK;AACzC;AACA,SAAS,OAAO,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAC9B,SAAO,QAAQ,cAAc,QAAQ,QAAQ,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;AAC7E;AACA,SAAS,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAChC,SAAO,OAAQ,IAAI,IAAM,CAAC,IAAI,GAAI,GAAG,GAAG,GAAG,GAAG,CAAC;AACnD;AACA,SAAS,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAChC,SAAO,OAAQ,IAAI,IAAM,IAAI,CAAC,GAAI,GAAG,GAAG,GAAG,GAAG,CAAC;AACnD;AACA,SAAS,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAChC,SAAO,OAAO,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC1C;AACA,SAAS,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAChC,SAAO,OAAO,KAAK,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC;AAC7C;AACA,IAAO,cAAQ;;;ACpIR,SAAS,cAAc,KAAK;AAC/B,QAAM,SAAS,mBAAmB,GAAG,CAAC;AACtC,QAAM,QAAQ,IAAI,WAAW,IAAI,MAAM;AACvC,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACjC,UAAM,CAAC,IAAI,IAAI,WAAW,CAAC;AAAA,EAC/B;AACA,SAAO;AACX;AACO,IAAM,MAAM;AACZ,IAAM,MAAM;AACJ,SAAR,IAAqBC,UAAS,MAAM,OAAO,WAAW,KAAK,QAAQ;AACtE,QAAM,aAAa,OAAO,UAAU,WAAW,cAAc,KAAK,IAAI;AACtE,QAAM,iBAAiB,OAAO,cAAc,WAAW,cAAM,SAAS,IAAI;AAC1E,MAAI,OAAO,cAAc,UAAU;AAC/B,gBAAY,cAAM,SAAS;AAAA,EAC/B;AACA,OAAI,uCAAW,YAAW,IAAI;AAC1B,UAAM,UAAU,kEAAkE;AAAA,EACtF;AACA,MAAI,QAAQ,IAAI,WAAW,KAAK,WAAW,MAAM;AACjD,QAAM,IAAI,cAAc;AACxB,QAAM,IAAI,YAAY,eAAe,MAAM;AAC3C,UAAQ,KAAK,KAAK;AAClB,QAAM,CAAC,IAAK,MAAM,CAAC,IAAI,KAAQA;AAC/B,QAAM,CAAC,IAAK,MAAM,CAAC,IAAI,KAAQ;AAC/B,MAAI,KAAK;AACL,aAAS,UAAU;AACnB,aAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AACzB,UAAI,SAAS,CAAC,IAAI,MAAM,CAAC;AAAA,IAC7B;AACA,WAAO;AAAA,EACX;AACA,SAAO,gBAAgB,KAAK;AAChC;;;AChCA,SAAS,GAAG,OAAO,WAAW,KAAK,QAAQ;AACvC,SAAO,IAAI,IAAM,aAAK,OAAO,WAAW,KAAK,MAAM;AACvD;AACA,GAAG,MAAM;AACT,GAAG,MAAM;AACT,IAAO,aAAQ;;;ACRf,IAAM,aAAa,OAAO,WAAW,eAAe,OAAO,cAAc,OAAO,WAAW,KAAK,MAAM;AACtG,IAAO,iBAAQ,EAAE,WAAW;;;ACE5B,SAAS,GAAG,SAAS,KAAK,QAAQ;AAHlC;AAII,MAAI,eAAO,cAAc,CAAC,OAAO,CAAC,SAAS;AACvC,WAAO,eAAO,WAAW;AAAA,EAC7B;AACA,YAAU,WAAW,CAAC;AACtB,QAAM,OAAO,QAAQ,YAAU,aAAQ,QAAR,qCAAmB,IAAI;AACtD,MAAI,KAAK,SAAS,IAAI;AAClB,UAAM,IAAI,MAAM,mCAAmC;AAAA,EACvD;AACA,OAAK,CAAC,IAAK,KAAK,CAAC,IAAI,KAAQ;AAC7B,OAAK,CAAC,IAAK,KAAK,CAAC,IAAI,KAAQ;AAC7B,MAAI,KAAK;AACL,aAAS,UAAU;AACnB,QAAI,SAAS,KAAK,SAAS,KAAK,IAAI,QAAQ;AACxC,YAAM,IAAI,WAAW,mBAAmB,MAAM,IAAI,SAAS,EAAE,0BAA0B;AAAA,IAC3F;AACA,aAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AACzB,UAAI,SAAS,CAAC,IAAI,KAAK,CAAC;AAAA,IAC5B;AACA,WAAO;AAAA,EACX;AACA,SAAO,gBAAgB,IAAI;AAC/B;AACA,IAAO,aAAQ;;;AC1Bf,SAAS,EAAE,GAAG,GAAG,GAAG,GAAG;AACnB,UAAQ,GAAG;AAAA,IACP,KAAK;AACD,aAAQ,IAAI,IAAM,CAAC,IAAI;AAAA,IAC3B,KAAK;AACD,aAAO,IAAI,IAAI;AAAA,IACnB,KAAK;AACD,aAAQ,IAAI,IAAM,IAAI,IAAM,IAAI;AAAA,IACpC,KAAK;AACD,aAAO,IAAI,IAAI;AAAA,EACvB;AACJ;AACA,SAAS,KAAK,GAAG,GAAG;AAChB,SAAQ,KAAK,IAAM,MAAO,KAAK;AACnC;AACA,SAAS,KAAK,OAAO;AACjB,QAAM,IAAI,CAAC,YAAY,YAAY,YAAY,UAAU;AACzD,QAAM,IAAI,CAAC,YAAY,YAAY,YAAY,WAAY,UAAU;AACrE,QAAM,WAAW,IAAI,WAAW,MAAM,SAAS,CAAC;AAChD,WAAS,IAAI,KAAK;AAClB,WAAS,MAAM,MAAM,IAAI;AACzB,UAAQ;AACR,QAAM,IAAI,MAAM,SAAS,IAAI;AAC7B,QAAM,IAAI,KAAK,KAAK,IAAI,EAAE;AAC1B,QAAM,IAAI,IAAI,MAAM,CAAC;AACrB,WAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACxB,UAAM,MAAM,IAAI,YAAY,EAAE;AAC9B,aAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AACzB,UAAI,CAAC,IACA,MAAM,IAAI,KAAK,IAAI,CAAC,KAAK,KACrB,MAAM,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,KAC7B,MAAM,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,IAC9B,MAAM,IAAI,KAAK,IAAI,IAAI,CAAC;AAAA,IACpC;AACA,MAAE,CAAC,IAAI;AAAA,EACX;AACA,IAAE,IAAI,CAAC,EAAE,EAAE,KAAM,MAAM,SAAS,KAAK,IAAK,KAAK,IAAI,GAAG,EAAE;AACxD,IAAE,IAAI,CAAC,EAAE,EAAE,IAAI,KAAK,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC;AACtC,IAAE,IAAI,CAAC,EAAE,EAAE,KAAM,MAAM,SAAS,KAAK,IAAK;AAC1C,WAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACxB,UAAM,IAAI,IAAI,YAAY,EAAE;AAC5B,aAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AACzB,QAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;AAAA,IACjB;AACA,aAAS,IAAI,IAAI,IAAI,IAAI,EAAE,GAAG;AAC1B,QAAE,CAAC,IAAI,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;AAAA,IAC9D;AACA,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,IAAI,EAAE,CAAC;AACX,QAAI,IAAI,EAAE,CAAC;AACX,aAAS,IAAI,GAAG,IAAI,IAAI,EAAE,GAAG;AACzB,YAAM,IAAI,KAAK,MAAM,IAAI,EAAE;AAC3B,YAAM,IAAK,KAAK,GAAG,CAAC,IAAI,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,MAAO;AAC7D,UAAI;AACJ,UAAI;AACJ,UAAI,KAAK,GAAG,EAAE,MAAM;AACpB,UAAI;AACJ,UAAI;AAAA,IACR;AACA,MAAE,CAAC,IAAK,EAAE,CAAC,IAAI,MAAO;AACtB,MAAE,CAAC,IAAK,EAAE,CAAC,IAAI,MAAO;AACtB,MAAE,CAAC,IAAK,EAAE,CAAC,IAAI,MAAO;AACtB,MAAE,CAAC,IAAK,EAAE,CAAC,IAAI,MAAO;AACtB,MAAE,CAAC,IAAK,EAAE,CAAC,IAAI,MAAO;AAAA,EAC1B;AACA,SAAO,WAAW,GAAG,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC;AACpO;AACA,IAAO,eAAQ;;;AClEf,SAAS,GAAG,OAAO,WAAW,KAAK,QAAQ;AACvC,SAAO,IAAI,IAAM,cAAM,OAAO,WAAW,KAAK,MAAM;AACxD;AACA,GAAG,MAAM;AACT,GAAG,MAAM;AACT,IAAO,aAAQ;;;ACLf,SAAS,GAAG,SAAS,KAAK,QAAQ;AAC9B,wBAAY,CAAC;AACb,sBAAW;AACX,MAAI,QAAQ,WAAG,EAAE,GAAG,SAAS,KAAK,KAAK,GAAG,IAAI,WAAW,EAAE,CAAC;AAC5D,UAAQ,OAAO,KAAK;AACpB,MAAI,KAAK;AACL,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AACzB,UAAI,SAAS,CAAC,IAAI,MAAM,CAAC;AAAA,IAC7B;AACA,WAAO;AAAA,EACX;AACA,SAAO,gBAAgB,KAAK;AAChC;AACA,IAAO,aAAQ;;;ACdA,SAAR,OAAwB,MAAM;AACjC,QAAM,UAAU,OAAO,SAAS,WAAW,cAAM,IAAI,IAAI;AACzD,QAAMC,WAAU,QAAQ,OAAO;AAC/B,SAAO,OAAO,SAAS,WAAW,gBAAgBA,QAAO,IAAIA;AACjE;AACA,SAAS,QAAQ,SAAS;AACtB,SAAO,WAAW,IAAK,QAAQ,CAAC,IAAI,OAAS,IAAO,QAAQ,CAAC,KAAK,IAAK,KAAS,QAAQ,CAAC,IAAI,OAAS,KAAO,QAAQ,CAAC,IAAI,QAAS,IAAM,QAAQ,CAAC,IAAI,OAAS,IAAM,QAAQ,CAAC,IAAI,IAAO,QAAQ,CAAC,IAAK,QAAQ,CAAC,IAAI,OAAS,KAAO,QAAQ,CAAC,IAAI,QAAS,IAAM,QAAQ,CAAC,IAAI,OAAS,KAAO,QAAQ,CAAC,IAAI,QAAS,GAAI,MAAS,QAAQ,CAAC,IAAI,QAAS,IAAM,QAAQ,CAAC,IAAI,OAAS,KAAO,QAAQ,CAAC,IAAI,QAAS,GAAI,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,QAAQ,EAAE,GAAG,QAAQ,EAAE,GAAG,QAAQ,EAAE,GAAG,QAAQ,EAAE,GAAG,QAAQ,EAAE,GAAG,QAAQ,EAAE,CAAC;AAC3f;;;ACPA,IAAMC,UAAS,CAAC;AAChB,SAAS,GAAG,SAAS,KAAK,QAAQ;AAHlC;AAII,MAAI;AACJ,MAAI,SAAS;AACT,YAAQ,QAAQ,QAAQ,YAAU,aAAQ,QAAR,qCAAmB,IAAI,GAAG,QAAQ,OAAO,QAAQ,KAAK,KAAK,MAAM;AAAA,EACvG,OACK;AACD,UAAM,MAAM,KAAK,IAAI;AACrB,UAAM,OAAO,IAAI;AACjB,kBAAcA,SAAQ,KAAK,IAAI;AAC/B,YAAQ,QAAQ,MAAMA,QAAO,OAAOA,QAAO,KAAK,KAAK,MAAM;AAAA,EAC/D;AACA,SAAO,OAAO,gBAAgB,KAAK;AACvC;AACO,SAAS,cAAc,OAAO,KAAK,MAAM;AAC5C,QAAM,UAAN,MAAM,QAAU;AAChB,QAAM,QAAN,MAAM,MAAQ;AACd,MAAI,MAAM,MAAM,OAAO;AACnB,UAAM,MAAO,KAAK,CAAC,KAAK,KAAO,KAAK,CAAC,KAAK,KAAO,KAAK,CAAC,KAAK,IAAK,KAAK,CAAC;AACvE,UAAM,QAAQ;AAAA,EAClB,OACK;AACD,UAAM,MAAO,MAAM,MAAM,IAAK;AAC9B,QAAI,MAAM,QAAQ,GAAG;AACjB,YAAM;AAAA,IACV;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,QAAQ,MAAM,OAAO,KAAK,KAAK,SAAS,GAAG;AAChD,MAAI,KAAK,SAAS,IAAI;AAClB,UAAM,IAAI,MAAM,mCAAmC;AAAA,EACvD;AACA,MAAI,CAAC,KAAK;AACN,UAAM,IAAI,WAAW,EAAE;AACvB,aAAS;AAAA,EACb,OACK;AACD,QAAI,SAAS,KAAK,SAAS,KAAK,IAAI,QAAQ;AACxC,YAAM,IAAI,WAAW,mBAAmB,MAAM,IAAI,SAAS,EAAE,0BAA0B;AAAA,IAC3F;AAAA,EACJ;AACA,oBAAU,KAAK,IAAI;AACnB,gBAAU,KAAK,CAAC,IAAI,OAAS,KAAO,KAAK,CAAC,KAAK,KAAO,KAAK,CAAC,KAAK,IAAK,KAAK,CAAC;AAC5E,MAAI,QAAQ,IAAK,QAAQ,gBAAiB;AAC1C,MAAI,QAAQ,IAAK,QAAQ,aAAe;AACxC,MAAI,QAAQ,IAAK,QAAQ,WAAa;AACtC,MAAI,QAAQ,IAAK,QAAQ,QAAW;AACpC,MAAI,QAAQ,IAAK,QAAQ,MAAS;AAClC,MAAI,QAAQ,IAAI,QAAQ;AACxB,MAAI,QAAQ,IAAI,MAAS,QAAQ,KAAM;AACvC,MAAI,QAAQ,IAAK,QAAQ,KAAM;AAC/B,MAAI,QAAQ,IAAI,MAAS,QAAQ,KAAM;AACvC,MAAI,QAAQ,IAAK,QAAQ,IAAK;AAC9B,MAAI,QAAQ,IAAM,OAAO,IAAK,MAAS,KAAK,EAAE,IAAI;AAClD,MAAI,QAAQ,IAAI,KAAK,EAAE;AACvB,MAAI,QAAQ,IAAI,KAAK,EAAE;AACvB,MAAI,QAAQ,IAAI,KAAK,EAAE;AACvB,MAAI,QAAQ,IAAI,KAAK,EAAE;AACvB,MAAI,QAAQ,IAAI,KAAK,EAAE;AACvB,SAAO;AACX;AACA,IAAO,aAAQ;;;AC/Df,SAAS,QAAQ,MAAM;AACnB,MAAI,CAAC,iBAAS,IAAI,GAAG;AACjB,UAAM,UAAU,cAAc;AAAA,EAClC;AACA,SAAO,SAAS,KAAK,MAAM,IAAI,EAAE,GAAG,EAAE;AAC1C;AACA,IAAO,kBAAQ;", "names": ["v1Bytes", "version", "v1Bytes", "_state"]}