import { Workflow, BaseNode, Connection } from '../types';
import { v4 as uuidv4 } from 'uuid';

/**
 * 工作流管理器 - 负责工作流的保存、加载、导入导出等功能
 */
export class WorkflowManager {
  private static readonly STORAGE_KEY = 'imageflow-workflows';
  private static readonly CURRENT_WORKFLOW_KEY = 'imageflow-current-workflow';

  /**
   * 保存工作流到本地存储
   */
  static saveWorkflow(workflow: Workflow): void {
    try {
      const workflows = this.getAllWorkflows();
      const existingIndex = workflows.findIndex(w => w.id === workflow.id);
      
      const updatedWorkflow = {
        ...workflow,
        updatedAt: new Date()
      };

      if (existingIndex >= 0) {
        workflows[existingIndex] = updatedWorkflow;
      } else {
        workflows.push(updatedWorkflow);
      }

      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(workflows));
      localStorage.setItem(this.CURRENT_WORKFLOW_KEY, JSON.stringify(updatedWorkflow));
    } catch (error) {
      console.error('保存工作流失败:', error);
      throw new Error('保存工作流失败');
    }
  }

  /**
   * 从本地存储加载工作流
   */
  static loadWorkflow(workflowId: string): Workflow | null {
    try {
      const workflows = this.getAllWorkflows();
      const workflow = workflows.find(w => w.id === workflowId);
      
      if (workflow) {
        localStorage.setItem(this.CURRENT_WORKFLOW_KEY, JSON.stringify(workflow));
      }
      
      return workflow || null;
    } catch (error) {
      console.error('加载工作流失败:', error);
      return null;
    }
  }

  /**
   * 获取当前工作流
   */
  static getCurrentWorkflow(): Workflow | null {
    try {
      const workflowData = localStorage.getItem(this.CURRENT_WORKFLOW_KEY);
      if (!workflowData) return null;
      
      const workflow = JSON.parse(workflowData);
      // 转换日期字符串为Date对象
      workflow.createdAt = new Date(workflow.createdAt);
      workflow.updatedAt = new Date(workflow.updatedAt);
      
      return workflow;
    } catch (error) {
      console.error('获取当前工作流失败:', error);
      return null;
    }
  }

  /**
   * 获取所有工作流
   */
  static getAllWorkflows(): Workflow[] {
    try {
      const workflowsData = localStorage.getItem(this.STORAGE_KEY);
      if (!workflowsData) return [];
      
      const workflows = JSON.parse(workflowsData);
      // 转换日期字符串为Date对象
      return workflows.map((workflow: any) => ({
        ...workflow,
        createdAt: new Date(workflow.createdAt),
        updatedAt: new Date(workflow.updatedAt)
      }));
    } catch (error) {
      console.error('获取工作流列表失败:', error);
      return [];
    }
  }

  /**
   * 删除工作流
   */
  static deleteWorkflow(workflowId: string): boolean {
    try {
      const workflows = this.getAllWorkflows();
      const filteredWorkflows = workflows.filter(w => w.id !== workflowId);
      
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(filteredWorkflows));
      
      // 如果删除的是当前工作流，清除当前工作流
      const currentWorkflow = this.getCurrentWorkflow();
      if (currentWorkflow && currentWorkflow.id === workflowId) {
        localStorage.removeItem(this.CURRENT_WORKFLOW_KEY);
      }
      
      return true;
    } catch (error) {
      console.error('删除工作流失败:', error);
      return false;
    }
  }

  /**
   * 复制工作流
   */
  static duplicateWorkflow(workflowId: string, newName?: string): Workflow | null {
    try {
      const originalWorkflow = this.loadWorkflow(workflowId);
      if (!originalWorkflow) return null;

      const duplicatedWorkflow: Workflow = {
        ...originalWorkflow,
        id: uuidv4(),
        name: newName || `${originalWorkflow.name} - 副本`,
        createdAt: new Date(),
        updatedAt: new Date(),
        // 复制节点，生成新的ID
        nodes: originalWorkflow.nodes.map(node => ({
          ...node,
          id: uuidv4()
        })),
        // 复制连接，更新节点ID引用
        connections: []
      };

      // 创建节点ID映射
      const nodeIdMap = new Map<string, string>();
      originalWorkflow.nodes.forEach((originalNode, index) => {
        nodeIdMap.set(originalNode.id, duplicatedWorkflow.nodes[index].id);
      });

      // 更新连接中的节点ID引用
      duplicatedWorkflow.connections = originalWorkflow.connections.map(connection => ({
        ...connection,
        id: uuidv4(),
        sourceNodeId: nodeIdMap.get(connection.sourceNodeId) || connection.sourceNodeId,
        targetNodeId: nodeIdMap.get(connection.targetNodeId) || connection.targetNodeId
      }));

      this.saveWorkflow(duplicatedWorkflow);
      return duplicatedWorkflow;
    } catch (error) {
      console.error('复制工作流失败:', error);
      return null;
    }
  }

  /**
   * 导出工作流为JSON文件
   */
  static exportWorkflow(workflow: Workflow): void {
    try {
      const dataStr = JSON.stringify(workflow, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      
      const link = document.createElement('a');
      link.href = URL.createObjectURL(dataBlob);
      link.download = `${workflow.name}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      URL.revokeObjectURL(link.href);
    } catch (error) {
      console.error('导出工作流失败:', error);
      throw new Error('导出工作流失败');
    }
  }

  /**
   * 从JSON文件导入工作流
   */
  static async importWorkflow(file: File): Promise<Workflow> {
    return new Promise((resolve, reject) => {
      try {
        const reader = new FileReader();
        
        reader.onload = (event) => {
          try {
            const workflowData = JSON.parse(event.target?.result as string);
            
            // 验证工作流数据格式
            if (!this.validateWorkflowData(workflowData)) {
              reject(new Error('无效的工作流文件格式'));
              return;
            }

            // 生成新的ID以避免冲突
            const importedWorkflow: Workflow = {
              ...workflowData,
              id: uuidv4(),
              name: `${workflowData.name} - 导入`,
              createdAt: new Date(),
              updatedAt: new Date(),
              nodes: workflowData.nodes.map((node: BaseNode) => ({
                ...node,
                id: uuidv4()
              })),
              connections: []
            };

            // 创建节点ID映射
            const nodeIdMap = new Map<string, string>();
            workflowData.nodes.forEach((originalNode: BaseNode, index: number) => {
              nodeIdMap.set(originalNode.id, importedWorkflow.nodes[index].id);
            });

            // 更新连接中的节点ID引用
            importedWorkflow.connections = workflowData.connections.map((connection: Connection) => ({
              ...connection,
              id: uuidv4(),
              sourceNodeId: nodeIdMap.get(connection.sourceNodeId) || connection.sourceNodeId,
              targetNodeId: nodeIdMap.get(connection.targetNodeId) || connection.targetNodeId
            }));

            this.saveWorkflow(importedWorkflow);
            resolve(importedWorkflow);
          } catch (parseError) {
            reject(new Error('解析工作流文件失败'));
          }
        };

        reader.onerror = () => {
          reject(new Error('读取文件失败'));
        };

        reader.readAsText(file);
      } catch (error) {
        reject(new Error('导入工作流失败'));
      }
    });
  }

  /**
   * 验证工作流数据格式
   */
  private static validateWorkflowData(data: any): boolean {
    return (
      data &&
      typeof data.id === 'string' &&
      typeof data.name === 'string' &&
      Array.isArray(data.nodes) &&
      Array.isArray(data.connections) &&
      data.createdAt &&
      data.updatedAt
    );
  }

  /**
   * 清除所有工作流数据
   */
  static clearAllWorkflows(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
      localStorage.removeItem(this.CURRENT_WORKFLOW_KEY);
    } catch (error) {
      console.error('清除工作流数据失败:', error);
    }
  }

  /**
   * 获取工作流统计信息
   */
  static getWorkflowStats(): {
    totalWorkflows: number;
    totalNodes: number;
    totalConnections: number;
    lastModified: Date | null;
  } {
    const workflows = this.getAllWorkflows();
    
    const totalNodes = workflows.reduce((sum, workflow) => sum + workflow.nodes.length, 0);
    const totalConnections = workflows.reduce((sum, workflow) => sum + workflow.connections.length, 0);
    
    const lastModified = workflows.length > 0
      ? new Date(Math.max(...workflows.map(w => w.updatedAt.getTime())))
      : null;

    return {
      totalWorkflows: workflows.length,
      totalNodes,
      totalConnections,
      lastModified
    };
  }

  /**
   * 搜索工作流
   */
  static searchWorkflows(query: string): Workflow[] {
    const workflows = this.getAllWorkflows();
    const lowercaseQuery = query.toLowerCase();

    return workflows.filter(workflow =>
      workflow.name.toLowerCase().includes(lowercaseQuery) ||
      workflow.nodes.some(node =>
        node.label.toLowerCase().includes(lowercaseQuery) ||
        node.type.toLowerCase().includes(lowercaseQuery)
      )
    );
  }

  /**
   * 自动保存当前工作流
   */
  static autoSaveCurrentWorkflow(workflow: Workflow): void {
    try {
      localStorage.setItem(this.CURRENT_WORKFLOW_KEY, JSON.stringify(workflow));
    } catch (error) {
      console.error('自动保存失败:', error);
    }
  }

  /**
   * 检查工作流名称是否已存在
   */
  static isWorkflowNameExists(name: string, excludeId?: string): boolean {
    const workflows = this.getAllWorkflows();
    return workflows.some(workflow =>
      workflow.name === name && workflow.id !== excludeId
    );
  }

  /**
   * 生成唯一的工作流名称
   */
  static generateUniqueWorkflowName(baseName: string): string {
    let counter = 1;
    let newName = baseName;

    while (this.isWorkflowNameExists(newName)) {
      newName = `${baseName} (${counter})`;
      counter++;
    }

    return newName;
  }
}
