import React, { useRef, useEffect, useState } from 'react';
import { Stage, Layer, Rect, Line } from 'react-konva';
import { useAppStore } from '../../stores/useAppStore';
import NodeRenderer from './NodeRenderer';
import ConnectionRenderer from './ConnectionRenderer';
import { generateGridLines, screenToCanvas } from '../../utils/canvasUtils';
import { NodeFactory } from '../../utils/nodeFactory';
import './Canvas.css';

const Canvas: React.FC = () => {
  const stageRef = useRef<any>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [dimensions, setDimensions] = useState({ width: 800, height: 600 });
  
  const {
    canvas,
    workflow,
    updateCanvas,
    addNode,
    selectNode,
    clearSelection
  } = useAppStore();

  // 监听容器尺寸变化
  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        const { clientWidth, clientHeight } = containerRef.current;
        setDimensions({ width: clientWidth, height: clientHeight });
      }
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    return () => window.removeEventListener('resize', updateDimensions);
  }, []);

  // 处理拖拽放置
  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    
    try {
      const data = JSON.parse(event.dataTransfer.getData('application/json'));
      if (data.type === 'node') {
        const stage = stageRef.current;
        const pointerPosition = stage.getPointerPosition();
        
        // 转换屏幕坐标到画布坐标
        const canvasPosition = {
          x: (pointerPosition.x - canvas.pan.x) / canvas.zoom,
          y: (pointerPosition.y - canvas.pan.y) / canvas.zoom
        };

        // 创建新节点
        const newNode = createNodeFromType(data.nodeType, canvasPosition);
        addNode(newNode);
      }
    } catch (error) {
      console.error('处理拖拽数据失败:', error);
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
  };

  // 根据节点类型创建节点
  const createNodeFromType = (nodeType: string, position: { x: number; y: number }) => {
    return NodeFactory.createNode(nodeType, position);
  };



  // 绘制网格
  const renderGrid = () => {
    if (!canvas.gridVisible) return null;

    const gridSize = 20;
    const { vertical, horizontal } = generateGridLines(
      dimensions.width,
      dimensions.height,
      gridSize,
      canvas.pan,
      canvas.zoom
    );

    const lines = [];

    // 垂直线
    vertical.forEach((x, index) => {
      lines.push(
        <Line
          key={`v-${index}`}
          points={[x, 0, x, dimensions.height]}
          stroke="#e8e8e8"
          strokeWidth={0.5}
          listening={false}
        />
      );
    });

    // 水平线
    horizontal.forEach((y, index) => {
      lines.push(
        <Line
          key={`h-${index}`}
          points={[0, y, dimensions.width, y]}
          stroke="#e8e8e8"
          strokeWidth={0.5}
          listening={false}
        />
      );
    });

    return lines;
  };

  // 处理画布点击
  const handleStageClick = (e: any) => {
    // 如果点击的是空白区域，清除选择
    if (e.target === e.target.getStage()) {
      clearSelection();
    }
  };

  // 处理滚轮缩放
  const handleWheel = (e: any) => {
    e.evt.preventDefault();
    
    const stage = stageRef.current;
    const oldScale = stage.scaleX();
    const pointer = stage.getPointerPosition();
    
    const scaleBy = 1.1;
    const newScale = e.evt.deltaY > 0 ? oldScale / scaleBy : oldScale * scaleBy;
    const clampedScale = Math.max(0.1, Math.min(5, newScale));
    
    updateCanvas({
      zoom: clampedScale,
      pan: {
        x: pointer.x - (pointer.x - canvas.pan.x) * (clampedScale / oldScale),
        y: pointer.y - (pointer.y - canvas.pan.y) * (clampedScale / oldScale)
      }
    });
  };

  return (
    <div 
      ref={containerRef}
      className="canvas-container"
      onDrop={handleDrop}
      onDragOver={handleDragOver}
    >
      <Stage
        ref={stageRef}
        width={dimensions.width}
        height={dimensions.height}
        scaleX={canvas.zoom}
        scaleY={canvas.zoom}
        x={canvas.pan.x}
        y={canvas.pan.y}
        onClick={handleStageClick}
        onWheel={handleWheel}
        draggable
        onDragEnd={(e) => {
          updateCanvas({
            pan: { x: e.target.x(), y: e.target.y() }
          });
        }}
      >
        <Layer>
          {/* 网格 */}
          {renderGrid()}
          
          {/* 背景 */}
          <Rect
            x={-canvas.pan.x / canvas.zoom}
            y={-canvas.pan.y / canvas.zoom}
            width={dimensions.width / canvas.zoom}
            height={dimensions.height / canvas.zoom}
            fill="#f5f5f5"
            listening={false}
          />
          
          {/* 渲染连接线 */}
          {workflow.connections.map(connection => {
            const sourceNode = workflow.nodes.find(node => node.id === connection.sourceNodeId);
            const targetNode = workflow.nodes.find(node => node.id === connection.targetNodeId);

            if (!sourceNode || !targetNode) return null;

            return (
              <ConnectionRenderer
                key={connection.id}
                connection={connection}
                sourceNode={sourceNode}
                targetNode={targetNode}
              />
            );
          })}

          {/* 渲染节点 */}
          {workflow.nodes.map(node => (
            <NodeRenderer
              key={node.id}
              node={node}
            />
          ))}
        </Layer>
      </Stage>
      
      {/* 画布信息显示 */}
      <div className="canvas-info">
        <span>缩放: {Math.round(canvas.zoom * 100)}%</span>
        <span>位置: ({Math.round(canvas.pan.x)}, {Math.round(canvas.pan.y)})</span>
        <span>节点数: {workflow.nodes.length}</span>
      </div>
    </div>
  );
};

export default Canvas;
