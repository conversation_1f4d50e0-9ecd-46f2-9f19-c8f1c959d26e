import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import QqOutlinedSvg from "@ant-design/icons-svg/es/asn/QqOutlined";
import AntdIcon from "../components/AntdIcon";
var QqOutlined = function QqOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: QqOutlinedSvg
  }));
};

/**![qq](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgyNC44IDYxMy4yYy0xNi01MS40LTM0LjQtOTQuNi02Mi43LTE2NS4zQzc2Ni41IDI2Mi4yIDY4OS4zIDExMiA1MTEuNSAxMTIgMzMxLjcgMTEyIDI1Ni4yIDI2NS4yIDI2MSA0NDcuOWMtMjguNCA3MC44LTQ2LjcgMTEzLjctNjIuNyAxNjUuMy0zNCAxMDkuNS0yMyAxNTQuOC0xNC42IDE1NS44IDE4IDIuMiA3MC4xLTgyLjQgNzAuMS04Mi40IDAgNDkgMjUuMiAxMTIuOSA3OS44IDE1OS0yNi40IDguMS04NS43IDI5LjktNzEuNiA1My44IDExLjQgMTkuMyAxOTYuMiAxMi4zIDI0OS41IDYuMyA1My4zIDYgMjM4LjEgMTMgMjQ5LjUtNi4zIDE0LjEtMjMuOC00NS4zLTQ1LjctNzEuNi01My44IDU0LjYtNDYuMiA3OS44LTExMC4xIDc5LjgtMTU5IDAgMCA1Mi4xIDg0LjYgNzAuMSA4Mi40IDguNS0xLjEgMTkuNS00Ni40LTE0LjUtMTU1Ljh6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(QqOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'QqOutlined';
}
export default RefIcon;