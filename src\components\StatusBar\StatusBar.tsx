import React from 'react';
import { Space, Typography, Progress, Tag, Tooltip } from 'antd';
import {
  CheckCircleOutlined,
  LoadingOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { useAppStore } from '../../stores/useAppStore';
import './StatusBar.css';

const { Text } = Typography;

const StatusBar: React.FC = () => {
  const { 
    workflow, 
    canvas, 
    selectedNodes, 
    isProcessing,
    history,
    historyIndex
  } = useAppStore();

  const getProcessingStatus = () => {
    if (isProcessing) {
      return {
        icon: <LoadingOutlined spin />,
        text: '正在处理...',
        color: '#1890ff'
      };
    }
    
    if (workflow.nodes.length === 0) {
      return {
        icon: <InfoCircleOutlined />,
        text: '就绪',
        color: '#666'
      };
    }
    
    return {
      icon: <CheckCircleOutlined />,
      text: '就绪',
      color: '#52c41a'
    };
  };

  const getMemoryUsage = () => {
    // 模拟内存使用情况
    const baseUsage = workflow.nodes.length * 5; // 每个节点约5MB
    const imageUsage = workflow.nodes.filter(node => 
      node.type === 'image-upload'
    ).length * 20; // 每张图片约20MB
    
    return Math.min(baseUsage + imageUsage, 512);
  };

  const getPerformanceInfo = () => {
    const memoryUsage = getMemoryUsage();
    const memoryPercent = (memoryUsage / 512) * 100;
    
    return {
      memory: memoryUsage,
      memoryPercent,
      fps: isProcessing ? 30 : 60 // 模拟FPS
    };
  };

  const status = getProcessingStatus();
  const performance = getPerformanceInfo();

  return (
    <div className="status-bar">
      <div className="status-bar-left">
        <Space size={16}>
          {/* 处理状态 */}
          <Space size={4}>
            <span style={{ color: status.color }}>
              {status.icon}
            </span>
            <Text style={{ fontSize: '12px', color: status.color }}>
              {status.text}
            </Text>
          </Space>

          {/* 工作流信息 */}
          <Space size={8}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              节点: {workflow.nodes.length}
            </Text>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              连接: {workflow.connections.length}
            </Text>
            {selectedNodes.length > 0 && (
              <Text type="secondary" style={{ fontSize: '12px' }}>
                已选择: {selectedNodes.length}
              </Text>
            )}
          </Space>

          {/* 历史状态 */}
          {history.length > 0 && (
            <Tooltip title={`历史记录: ${historyIndex + 1}/${history.length}`}>
              <Tag size="small" color="blue">
                {historyIndex + 1}/{history.length}
              </Tag>
            </Tooltip>
          )}
        </Space>
      </div>

      <div className="status-bar-center">
        {isProcessing && (
          <Space size={8}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              处理进度:
            </Text>
            <Progress
              percent={65} // 模拟进度
              size="small"
              style={{ width: '100px' }}
              showInfo={false}
            />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              65%
            </Text>
          </Space>
        )}
      </div>

      <div className="status-bar-right">
        <Space size={16}>
          {/* 性能信息 */}
          <Space size={8}>
            <Tooltip title={`内存使用: ${performance.memory}MB / 512MB`}>
              <Space size={4}>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  内存:
                </Text>
                <Progress
                  percent={performance.memoryPercent}
                  size="small"
                  style={{ width: '60px' }}
                  showInfo={false}
                  strokeColor={
                    performance.memoryPercent > 80 ? '#ff4d4f' :
                    performance.memoryPercent > 60 ? '#faad14' : '#52c41a'
                  }
                />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {performance.memory}MB
                </Text>
              </Space>
            </Tooltip>

            <Text type="secondary" style={{ fontSize: '12px' }}>
              FPS: {performance.fps}
            </Text>
          </Space>

          {/* 画布信息 */}
          <Space size={8}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              缩放: {Math.round(canvas.zoom * 100)}%
            </Text>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              坐标: ({Math.round(canvas.pan.x)}, {Math.round(canvas.pan.y)})
            </Text>
          </Space>

          {/* 版本信息 */}
          <Text type="secondary" style={{ fontSize: '12px' }}>
            ImageFlow v1.0.0
          </Text>
        </Space>
      </div>
    </div>
  );
};

export default StatusBar;
