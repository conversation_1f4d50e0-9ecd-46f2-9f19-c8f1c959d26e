"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _RadiusSettingOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/RadiusSettingOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var RadiusSettingOutlined = function RadiusSettingOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _RadiusSettingOutlined.default
  }));
};

/**![radius-setting](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTM5NiAxNDBoLTU2Yy00LjQgMC04IDMuNi04IDh2NTZjMCA0LjQgMy42IDggOCA4aDU2YzQuNCAwIDgtMy42IDgtOHYtNTZjMC00LjQtMy42LTgtOC04em0tNDQgNjg0aC01NmMtNC40IDAtOCAzLjYtOCA4djU2YzAgNC40IDMuNiA4IDggOGg1NmM0LjQgMCA4LTMuNiA4LTh2LTU2YzAtNC40LTMuNi04LTgtOHptNTI0LTIwNGgtNTZjLTQuNCAwLTggMy42LTggOHY1NmMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04di01NmMwLTQuNC0zLjYtOC04LTh6TTE5MiAzNDRoLTU2Yy00LjQgMC04IDMuNi04IDh2NTZjMCA0LjQgMy42IDggOCA4aDU2YzQuNCAwIDgtMy42IDgtOHYtNTZjMC00LjQtMy42LTgtOC04em0wIDE2MGgtNTZjLTQuNCAwLTggMy42LTggOHY1NmMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04di01NmMwLTQuNC0zLjYtOC04LTh6bTAgMTYwaC01NmMtNC40IDAtOCAzLjYtOCA4djU2YzAgNC40IDMuNiA4IDggOGg1NmM0LjQgMCA4LTMuNiA4LTh2LTU2YzAtNC40LTMuNi04LTgtOHptMCAxNjBoLTU2Yy00LjQgMC04IDMuNi04IDh2NTZjMCA0LjQgMy42IDggOCA4aDU2YzQuNCAwIDgtMy42IDgtOHYtNTZjMC00LjQtMy42LTgtOC04em0zMjAgMGgtNTZjLTQuNCAwLTggMy42LTggOHY1NmMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04di01NmMwLTQuNC0zLjYtOC04LTh6bTE2MCAwaC01NmMtNC40IDAtOCAzLjYtOCA4djU2YzAgNC40IDMuNiA4IDggOGg1NmM0LjQgMCA4LTMuNiA4LTh2LTU2YzAtNC40LTMuNi04LTgtOHptMTQwLTI4NGMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04VjM3MGMwLTEyNy0xMDMtMjMwLTIzMC0yMzBINDg0Yy00LjQgMC04IDMuNi04IDh2NTZjMCA0LjQgMy42IDggOCA4aDE3MGM4Ny4zIDAgMTU4IDcwLjcgMTU4IDE1OHYxNzB6TTIzNiA5Nkg5MmMtNC40IDAtOCAzLjYtOCA4djE0NGMwIDQuNCAzLjYgOCA4IDhoMTQ0YzQuNCAwIDgtMy42IDgtOFYxMDRjMC00LjQtMy42LTgtOC04em0tNDggMTAxLjZjMCAxLjMtMS4xIDIuNC0yLjQgMi40aC00My4yYy0xLjMgMC0yLjQtMS4xLTIuNC0yLjR2LTQzLjJjMC0xLjMgMS4xLTIuNCAyLjQtMi40aDQzLjJjMS4zIDAgMi40IDEuMSAyLjQgMi40djQzLjJ6TTkyMCA3ODBINzc2Yy00LjQgMC04IDMuNi04IDh2MTQ0YzAgNC40IDMuNiA4IDggOGgxNDRjNC40IDAgOC0zLjYgOC04Vjc4OGMwLTQuNC0zLjYtOC04LTh6bS00OCAxMDEuNmMwIDEuMy0xLjEgMi40LTIuNCAyLjRoLTQzLjJjLTEuMyAwLTIuNC0xLjEtMi40LTIuNHYtNDMuMmMwLTEuMyAxLjEtMi40IDIuNC0yLjRoNDMuMmMxLjMgMCAyLjQgMS4xIDIuNCAyLjR2NDMuMnoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(RadiusSettingOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'RadiusSettingOutlined';
}
var _default = exports.default = RefIcon;