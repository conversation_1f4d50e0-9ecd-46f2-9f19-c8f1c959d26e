import { BaseNode, Connection, ImageData } from '../types';
import { WorkflowEngine } from './workflowEngine';
import { ConnectionManager } from './connectionManager';

/**
 * 预览管理器 - 负责管理节点的实时预览
 */
export class PreviewManager {
  private nodes: BaseNode[];
  private connections: Connection[];
  private previews: Map<string, ImageData | null>;
  private previewCallbacks: Map<string, (preview: ImageData | null) => void>;
  private engine: WorkflowEngine;
  private isUpdating: boolean = false;
  private updateQueue: Set<string> = new Set();

  constructor(nodes: BaseNode[], connections: Connection[]) {
    this.nodes = nodes;
    this.connections = connections;
    this.previews = new Map();
    this.previewCallbacks = new Map();
    this.engine = new WorkflowEngine(nodes, connections);
  }

  /**
   * 更新节点和连接
   */
  updateWorkflow(nodes: BaseNode[], connections: Connection[]) {
    this.nodes = nodes;
    this.connections = connections;
    this.engine = new WorkflowEngine(nodes, connections);
  }

  /**
   * 注册预览回调
   */
  registerPreviewCallback(nodeId: string, callback: (preview: ImageData | null) => void) {
    this.previewCallbacks.set(nodeId, callback);
  }

  /**
   * 注销预览回调
   */
  unregisterPreviewCallback(nodeId: string) {
    this.previewCallbacks.delete(nodeId);
  }

  /**
   * 获取节点预览
   */
  getPreview(nodeId: string): ImageData | null {
    return this.previews.get(nodeId) || null;
  }

  /**
   * 设置节点预览
   */
  setPreview(nodeId: string, preview: ImageData | null) {
    this.previews.set(nodeId, preview);
    
    // 通知回调
    const callback = this.previewCallbacks.get(nodeId);
    if (callback) {
      callback(preview);
    }
  }

  /**
   * 更新节点预览
   */
  async updateNodePreview(nodeId: string, force: boolean = false) {
    if (this.isUpdating && !force) {
      this.updateQueue.add(nodeId);
      return;
    }

    try {
      this.isUpdating = true;

      const node = this.nodes.find(n => n.id === nodeId);
      if (!node) {
        console.warn(`节点不存在: ${nodeId}`);
        return;
      }

      // 检查节点是否需要更新
      if (!force && !this.shouldUpdateNode(node)) {
        return;
      }

      // 获取节点的依赖
      const dependencies = ConnectionManager.getNodeDependencies(nodeId, this.connections);
      
      // 确保所有依赖都有预览
      for (const depId of dependencies) {
        if (!this.previews.has(depId)) {
          await this.updateNodePreview(depId, true);
        }
      }

      // 执行节点处理
      const result = await this.engine.executeFromNode(nodeId);
      const nodeResult = result.get(nodeId);

      if (nodeResult instanceof Blob) {
        // 如果是Blob，转换为ImageData
        const imageData = await this.blobToImageData(nodeResult);
        this.setPreview(nodeId, imageData);
      } else {
        this.setPreview(nodeId, nodeResult as ImageData || null);
      }

      // 更新依赖此节点的其他节点
      const dependents = ConnectionManager.getNodeDependents(nodeId, this.connections);
      for (const depId of dependents) {
        this.updateQueue.add(depId);
      }

    } catch (error) {
      console.error(`更新节点预览失败 ${nodeId}:`, error);
      this.setPreview(nodeId, null);
    } finally {
      this.isUpdating = false;
      
      // 处理队列中的更新
      if (this.updateQueue.size > 0) {
        const nextNodeId = this.updateQueue.values().next().value;
        this.updateQueue.delete(nextNodeId);
        setTimeout(() => this.updateNodePreview(nextNodeId), 0);
      }
    }
  }

  /**
   * 检查节点是否需要更新
   */
  private shouldUpdateNode(node: BaseNode): boolean {
    // 输入节点总是需要更新
    if (node.type === 'image-upload') {
      return true;
    }

    // 检查输入是否有变化
    const inputConnections = ConnectionManager.getNodeInputConnections(node.id, this.connections);
    for (const connection of inputConnections) {
      const sourcePreview = this.previews.get(connection.sourceNodeId);
      if (!sourcePreview) {
        return false; // 输入还没准备好
      }
    }

    return true;
  }

  /**
   * 将Blob转换为ImageData
   */
  private async blobToImageData(blob: Blob): Promise<ImageData> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        resolve({
          id: `preview_${Date.now()}`,
          data: img,
          width: img.width,
          height: img.height,
          format: blob.type
        });
      };
      img.onerror = reject;
      img.src = URL.createObjectURL(blob);
    });
  }

  /**
   * 批量更新预览
   */
  async updateAllPreviews() {
    try {
      // 获取执行顺序
      const executionOrder = ConnectionManager.getExecutionOrder(this.nodes, this.connections);
      
      // 按顺序更新预览
      for (const nodeId of executionOrder) {
        await this.updateNodePreview(nodeId, true);
      }
    } catch (error) {
      console.error('批量更新预览失败:', error);
    }
  }

  /**
   * 清除所有预览
   */
  clearAllPreviews() {
    this.previews.clear();
    
    // 通知所有回调
    for (const [nodeId, callback] of this.previewCallbacks) {
      callback(null);
    }
  }

  /**
   * 清除节点预览
   */
  clearNodePreview(nodeId: string) {
    this.previews.delete(nodeId);
    
    const callback = this.previewCallbacks.get(nodeId);
    if (callback) {
      callback(null);
    }

    // 清除依赖此节点的其他节点预览
    const dependents = ConnectionManager.getNodeDependents(nodeId, this.connections);
    for (const depId of dependents) {
      this.clearNodePreview(depId);
    }
  }

  /**
   * 获取预览统计信息
   */
  getPreviewStats(): {
    totalNodes: number;
    previewedNodes: number;
    pendingNodes: number;
  } {
    const totalNodes = this.nodes.length;
    const previewedNodes = this.previews.size;
    const pendingNodes = this.updateQueue.size;

    return {
      totalNodes,
      previewedNodes,
      pendingNodes
    };
  }

  /**
   * 检查是否正在更新
   */
  isUpdatingPreviews(): boolean {
    return this.isUpdating || this.updateQueue.size > 0;
  }

  /**
   * 获取所有预览
   */
  getAllPreviews(): Map<string, ImageData | null> {
    return new Map(this.previews);
  }

  /**
   * 销毁预览管理器
   */
  destroy() {
    this.previews.clear();
    this.previewCallbacks.clear();
    this.updateQueue.clear();
    this.isUpdating = false;
  }
}

/**
 * 全局预览管理器实例
 */
let globalPreviewManager: PreviewManager | null = null;

/**
 * 获取全局预览管理器
 */
export const getPreviewManager = (): PreviewManager | null => {
  return globalPreviewManager;
};

/**
 * 设置全局预览管理器
 */
export const setPreviewManager = (manager: PreviewManager) => {
  globalPreviewManager = manager;
};

/**
 * 清除全局预览管理器
 */
export const clearPreviewManager = () => {
  if (globalPreviewManager) {
    globalPreviewManager.destroy();
    globalPreviewManager = null;
  }
};
