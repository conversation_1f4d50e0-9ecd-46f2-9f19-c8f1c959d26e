// 节点类型定义
export interface NodePosition {
  x: number;
  y: number;
}

export interface NodeSize {
  width: number;
  height: number;
}

export interface NodeConnection {
  id: string;
  nodeId: string;
  type: 'input' | 'output';
  dataType: 'image' | 'number' | 'color' | 'boolean';
  label: string;
}

export interface BaseNode {
  id: string;
  type: string;
  position: NodePosition;
  size: NodeSize;
  inputs: NodeConnection[];
  outputs: NodeConnection[];
  parameters: Record<string, any>;
  label: string;
  selected: boolean;
}

// 连接线定义
export interface Connection {
  id: string;
  sourceNodeId: string;
  sourceConnectionId: string;
  targetNodeId: string;
  targetConnectionId: string;
}

// 工作流定义
export interface Workflow {
  id: string;
  name: string;
  nodes: BaseNode[];
  connections: Connection[];
  createdAt: Date;
  updatedAt: Date;
}

// 画布状态
export interface CanvasState {
  zoom: number;
  pan: NodePosition;
  gridVisible: boolean;
  snapToGrid: boolean;
}

// 图像数据
export interface ImageData {
  id: string;
  data: HTMLImageElement | HTMLCanvasElement;
  width: number;
  height: number;
  format: string;
}

// 节点参数类型
export interface NodeParameter {
  name: string;
  type: 'number' | 'string' | 'boolean' | 'color' | 'select' | 'range';
  value: any;
  min?: number;
  max?: number;
  step?: number;
  options?: Array<{ label: string; value: any }>;
  label: string;
  description?: string;
}

// 节点类别
export interface NodeCategory {
  id: string;
  name: string;
  icon: string;
  nodes: string[];
}

// 应用状态
export interface AppState {
  workflow: Workflow;
  canvas: CanvasState;
  selectedNodes: string[];
  clipboard: BaseNode[];
  history: Workflow[];
  historyIndex: number;
  isProcessing: boolean;
}
