import Konva from 'konva';
import React, { HTMLAttributes, PropsWithChildren } from 'react';
export type HtmlTransformAttrs = {
    x: number;
    y: number;
    scaleX: number;
    scaleY: number;
    rotation: number;
    skewX: number;
    skewY: number;
};
export type HtmlProps = PropsWithChildren<{
    groupProps?: Konva.ContainerConfig;
    divProps?: HTMLAttributes<HTMLDivElement>;
    transform?: boolean;
    transformFunc?: (attrs: HtmlTransformAttrs) => HtmlTransformAttrs;
    parentNodeFunc?: (args: {
        stage: Konva.Stage | null;
    }) => HTMLDivElement;
}>;
export declare function useEvent(fn?: () => void): (...args: any[]) => any;
export declare const Html: ({ children, groupProps, divProps, transform, transformFunc, parentNodeFunc, }: HtmlProps) => React.JSX.Element;
