{"name": "@types/fabric", "version": "5.3.10", "description": "TypeScript definitions for fabric", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/fabric", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/oklemencic"}, {"name": "<PERSON>", "githubUsername": "joewashear007", "url": "https://github.com/joewashear007"}, {"name": "<PERSON>", "githubUsername": "mrand01", "url": "https://github.com/mrand01"}, {"name": "<PERSON>", "githubUsername": "NotWoods", "url": "https://github.com/NotWoods"}, {"name": "<PERSON>", "githubUsername": "b<PERSON><PERSON><PERSON>", "url": "https://github.com/bmartinson"}, {"name": "<PERSON><PERSON>", "githubUsername": "Roger<PERSON>Teixeira", "url": "https://github.com/RogerioTeixeira"}, {"name": "<PERSON>", "githubUsername": "BradleyHill", "url": "https://github.com/BradleyHill"}, {"name": "<PERSON>", "githubUsername": "bmkrol823", "url": "https://github.com/bmkrol823"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/glenngartner"}, {"name": "Codertx", "githubUsername": "codertx", "url": "https://github.com/codertx"}, {"name": "<PERSON>", "githubUsername": "mike667", "url": "https://github.com/mike667"}, {"name": "<PERSON>", "githubUsername": "natal<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/nataliemarleny"}, {"name": "<PERSON>", "githubUsername": "oxwazz", "url": "https://github.com/oxwazz"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/fabric"}, "scripts": {}, "dependencies": {}, "peerDependencies": {}, "typesPublisherContentHash": "7ea6f78d6fe117a75ed0aecf4fb40f76d1c54de5f6f29b34babfa5d9eed6bf8f", "typeScriptVersion": "5.0"}