import { NodePosition } from '../types';

/**
 * 将屏幕坐标转换为画布坐标
 */
export const screenToCanvas = (
  screenPos: NodePosition,
  canvasPos: NodePosition,
  zoom: number
): NodePosition => {
  return {
    x: (screenPos.x - canvasPos.x) / zoom,
    y: (screenPos.y - canvasPos.y) / zoom
  };
};

/**
 * 将画布坐标转换为屏幕坐标
 */
export const canvasToScreen = (
  canvasPos: NodePosition,
  panOffset: NodePosition,
  zoom: number
): NodePosition => {
  return {
    x: canvasPos.x * zoom + panOffset.x,
    y: canvasPos.y * zoom + panOffset.y
  };
};

/**
 * 限制缩放级别
 */
export const clampZoom = (zoom: number, min = 0.1, max = 5): number => {
  return Math.max(min, Math.min(max, zoom));
};

/**
 * 对齐到网格
 */
export const snapToGrid = (
  position: NodePosition,
  gridSize = 20,
  enabled = true
): NodePosition => {
  if (!enabled) return position;
  
  return {
    x: Math.round(position.x / gridSize) * gridSize,
    y: Math.round(position.y / gridSize) * gridSize
  };
};

/**
 * 计算两点之间的距离
 */
export const distance = (p1: NodePosition, p2: NodePosition): number => {
  const dx = p2.x - p1.x;
  const dy = p2.y - p1.y;
  return Math.sqrt(dx * dx + dy * dy);
};

/**
 * 计算矩形是否相交
 */
export const rectIntersects = (
  rect1: { x: number; y: number; width: number; height: number },
  rect2: { x: number; y: number; width: number; height: number }
): boolean => {
  return !(
    rect1.x + rect1.width < rect2.x ||
    rect2.x + rect2.width < rect1.x ||
    rect1.y + rect1.height < rect2.y ||
    rect2.y + rect2.height < rect1.y
  );
};

/**
 * 计算点是否在矩形内
 */
export const pointInRect = (
  point: NodePosition,
  rect: { x: number; y: number; width: number; height: number }
): boolean => {
  return (
    point.x >= rect.x &&
    point.x <= rect.x + rect.width &&
    point.y >= rect.y &&
    point.y <= rect.y + rect.height
  );
};

/**
 * 生成网格线坐标
 */
export const generateGridLines = (
  width: number,
  height: number,
  gridSize: number,
  offset: NodePosition,
  zoom: number
): { vertical: number[]; horizontal: number[] } => {
  const scaledGridSize = gridSize * zoom;
  const startX = (-offset.x % scaledGridSize) - scaledGridSize;
  const startY = (-offset.y % scaledGridSize) - scaledGridSize;
  
  const vertical: number[] = [];
  const horizontal: number[] = [];
  
  // 垂直线
  for (let x = startX; x <= width + scaledGridSize; x += scaledGridSize) {
    vertical.push(x);
  }
  
  // 水平线
  for (let y = startY; y <= height + scaledGridSize; y += scaledGridSize) {
    horizontal.push(y);
  }
  
  return { vertical, horizontal };
};

/**
 * 计算贝塞尔曲线控制点
 */
export const getBezierControlPoints = (
  start: NodePosition,
  end: NodePosition,
  curvature = 0.3
): { cp1: NodePosition; cp2: NodePosition } => {
  const dx = end.x - start.x;
  const offset = Math.abs(dx) * curvature;
  
  return {
    cp1: { x: start.x + offset, y: start.y },
    cp2: { x: end.x - offset, y: end.y }
  };
};

/**
 * 生成贝塞尔曲线路径
 */
export const generateBezierPath = (
  start: NodePosition,
  end: NodePosition,
  curvature = 0.3
): string => {
  const { cp1, cp2 } = getBezierControlPoints(start, end, curvature);
  
  return `M ${start.x} ${start.y} C ${cp1.x} ${cp1.y}, ${cp2.x} ${cp2.y}, ${end.x} ${end.y}`;
};

/**
 * 计算视口边界
 */
export const getViewportBounds = (
  width: number,
  height: number,
  pan: NodePosition,
  zoom: number
) => {
  const topLeft = screenToCanvas({ x: 0, y: 0 }, pan, zoom);
  const bottomRight = screenToCanvas({ x: width, y: height }, pan, zoom);
  
  return {
    left: topLeft.x,
    top: topLeft.y,
    right: bottomRight.x,
    bottom: bottomRight.y,
    width: bottomRight.x - topLeft.x,
    height: bottomRight.y - topLeft.y
  };
};

/**
 * 限制平移范围
 */
export const clampPan = (
  pan: NodePosition,
  bounds: { minX: number; maxX: number; minY: number; maxY: number }
): NodePosition => {
  return {
    x: Math.max(bounds.minX, Math.min(bounds.maxX, pan.x)),
    y: Math.max(bounds.minY, Math.min(bounds.maxY, pan.y))
  };
};
