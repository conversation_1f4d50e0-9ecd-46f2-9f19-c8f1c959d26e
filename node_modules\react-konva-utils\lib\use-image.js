"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.useImage = void 0;
var use_image_1 = require("use-image");
Object.defineProperty(exports, "useImage", { enumerable: true, get: function () { return __importDefault(use_image_1).default; } });
