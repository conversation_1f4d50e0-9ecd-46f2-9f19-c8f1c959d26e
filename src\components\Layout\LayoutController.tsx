import React, { useState, useEffect } from 'react';
import { Button, Tooltip } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  SettingOutlined
} from '@ant-design/icons';

interface LayoutControllerProps {
  leftSiderCollapsed: boolean;
  rightSiderCollapsed: boolean;
  onLeftSiderToggle: () => void;
  onRightSiderToggle: () => void;
  onFullscreenToggle?: () => void;
}

const LayoutController: React.FC<LayoutControllerProps> = ({
  leftSiderCollapsed,
  rightSiderCollapsed,
  onLeftSiderToggle,
  onRightSiderToggle,
  onFullscreenToggle
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);

  // 检测全屏状态变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  const handleFullscreenToggle = async () => {
    try {
      if (!document.fullscreenElement) {
        await document.documentElement.requestFullscreen();
      } else {
        await document.exitFullscreen();
      }
      onFullscreenToggle?.();
    } catch (error) {
      console.error('全屏切换失败:', error);
    }
  };

  return (
    <>
      {/* 左侧边栏切换按钮 */}
      <div className="sider-toggle-btn sider-toggle-btn-left">
        <Tooltip 
          title={leftSiderCollapsed ? "显示节点面板" : "隐藏节点面板"} 
          placement="right"
        >
          <Button
            type="text"
            size="small"
            icon={leftSiderCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={onLeftSiderToggle}
            style={{ 
              border: 'none',
              boxShadow: 'none',
              width: '100%',
              height: '100%'
            }}
          />
        </Tooltip>
      </div>

      {/* 右侧边栏切换按钮 */}
      <div className="sider-toggle-btn sider-toggle-btn-right">
        <Tooltip 
          title={rightSiderCollapsed ? "显示属性面板" : "隐藏属性面板"} 
          placement="left"
        >
          <Button
            type="text"
            size="small"
            icon={rightSiderCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={onRightSiderToggle}
            style={{ 
              border: 'none',
              boxShadow: 'none',
              width: '100%',
              height: '100%',
              transform: 'rotate(180deg)'
            }}
          />
        </Tooltip>
      </div>

      {/* 全屏切换按钮 */}
      <div 
        style={{
          position: 'absolute',
          top: '16px',
          right: '16px',
          zIndex: 102
        }}
      >
        <Tooltip title={isFullscreen ? "退出全屏" : "进入全屏"}>
          <Button
            type="text"
            icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
            onClick={handleFullscreenToggle}
            style={{
              background: 'rgba(255, 255, 255, 0.9)',
              border: '1px solid #e8e8e8',
              borderRadius: '6px',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
            }}
          />
        </Tooltip>
      </div>
    </>
  );
};

export default LayoutController;
