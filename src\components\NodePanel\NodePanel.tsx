import React, { useState } from 'react';
import { 
  Input, 
  Collapse, 
  Card, 
  Typography, 
  Space,
  Tooltip
} from 'antd';
import {
  SearchOutlined,
  FileImageOutlined,
  ScissorOutlined,
  BgColorsOutlined,
  FilterOutlined,
  ExportOutlined,
  RotateLeftOutlined,
  ZoomInOutlined,
  AdjustmentOutlined,
  BlurOutlined
} from '@ant-design/icons';
import { NodeCategory } from '../../types';
import './NodePanel.css';

const { Search } = Input;
const { Panel } = Collapse;
const { Text } = Typography;

const NodePanel: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [activeKey, setActiveKey] = useState<string[]>(['input', 'transform', 'color']);

  // 节点类别定义
  const nodeCategories: NodeCategory[] = [
    {
      id: 'input',
      name: '输入',
      icon: 'FileImageOutlined',
      nodes: ['image-upload']
    },
    {
      id: 'transform',
      name: '变换',
      icon: 'RotateLeftOutlined',
      nodes: ['resize', 'rotate', 'crop', 'flip']
    },
    {
      id: 'color',
      name: '颜色调整',
      icon: 'BgColorsOutlined',
      nodes: ['brightness-contrast', 'hue-saturation', 'color-balance', 'curves', 'levels']
    },
    {
      id: 'filter',
      name: '滤镜效果',
      icon: 'FilterOutlined',
      nodes: ['blur', 'sharpen', 'noise', 'edge-detection', 'emboss']
    },
    {
      id: 'output',
      name: '输出',
      icon: 'ExportOutlined',
      nodes: ['image-export']
    }
  ];

  // 节点定义
  const nodeDefinitions = {
    'image-upload': {
      name: '图片上传',
      description: '上传图片文件',
      icon: <FileImageOutlined />,
      color: '#52c41a'
    },
    'resize': {
      name: '缩放',
      description: '调整图片尺寸',
      icon: <ZoomInOutlined />,
      color: '#1890ff'
    },
    'rotate': {
      name: '旋转',
      description: '旋转图片',
      icon: <RotateLeftOutlined />,
      color: '#1890ff'
    },
    'crop': {
      name: '裁剪',
      description: '裁剪图片',
      icon: <ScissorOutlined />,
      color: '#1890ff'
    },
    'flip': {
      name: '翻转',
      description: '水平或垂直翻转',
      icon: <RotateLeftOutlined />,
      color: '#1890ff'
    },
    'brightness-contrast': {
      name: '亮度对比度',
      description: '调整亮度和对比度',
      icon: <AdjustmentOutlined />,
      color: '#fa8c16'
    },
    'hue-saturation': {
      name: '色调饱和度',
      description: '调整色调和饱和度',
      icon: <BgColorsOutlined />,
      color: '#fa8c16'
    },
    'color-balance': {
      name: '色彩平衡',
      description: '调整色彩平衡',
      icon: <BgColorsOutlined />,
      color: '#fa8c16'
    },
    'curves': {
      name: '曲线调整',
      description: '使用曲线调整色调',
      icon: <AdjustmentOutlined />,
      color: '#fa8c16'
    },
    'levels': {
      name: '色阶调整',
      description: '调整色阶',
      icon: <AdjustmentOutlined />,
      color: '#fa8c16'
    },
    'blur': {
      name: '模糊',
      description: '高斯模糊效果',
      icon: <BlurOutlined />,
      color: '#722ed1'
    },
    'sharpen': {
      name: '锐化',
      description: '锐化图片',
      icon: <FilterOutlined />,
      color: '#722ed1'
    },
    'noise': {
      name: '噪点',
      description: '添加或去除噪点',
      icon: <FilterOutlined />,
      color: '#722ed1'
    },
    'edge-detection': {
      name: '边缘检测',
      description: '检测图片边缘',
      icon: <FilterOutlined />,
      color: '#722ed1'
    },
    'emboss': {
      name: '浮雕',
      description: '浮雕效果',
      icon: <FilterOutlined />,
      color: '#722ed1'
    },
    'image-export': {
      name: '图片导出',
      description: '导出处理后的图片',
      icon: <ExportOutlined />,
      color: '#f5222d'
    }
  };

  const handleDragStart = (event: React.DragEvent, nodeType: string) => {
    event.dataTransfer.setData('application/json', JSON.stringify({
      type: 'node',
      nodeType: nodeType
    }));
  };

  const filteredCategories = nodeCategories.map(category => ({
    ...category,
    nodes: category.nodes.filter(nodeType => {
      const node = nodeDefinitions[nodeType as keyof typeof nodeDefinitions];
      return node.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
             node.description.toLowerCase().includes(searchTerm.toLowerCase());
    })
  })).filter(category => category.nodes.length > 0);

  const renderNodeItem = (nodeType: string) => {
    const node = nodeDefinitions[nodeType as keyof typeof nodeDefinitions];
    if (!node) return null;

    return (
      <Card
        key={nodeType}
        className="node-item"
        size="small"
        draggable
        onDragStart={(e) => handleDragStart(e, nodeType)}
        hoverable
      >
        <div className="node-item-content">
          <div 
            className="node-item-icon"
            style={{ color: node.color }}
          >
            {node.icon}
          </div>
          <div className="node-item-info">
            <Text strong className="node-item-name">
              {node.name}
            </Text>
            <Text type="secondary" className="node-item-description">
              {node.description}
            </Text>
          </div>
        </div>
      </Card>
    );
  };

  return (
    <div className="node-panel">
      <div className="node-panel-header">
        <Text strong style={{ fontSize: '16px' }}>节点库</Text>
        <Search
          placeholder="搜索节点..."
          prefix={<SearchOutlined />}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          style={{ marginTop: '12px' }}
          allowClear
        />
      </div>

      <div className="node-panel-content">
        <Collapse
          activeKey={activeKey}
          onChange={(keys) => setActiveKey(keys as string[])}
          ghost
          size="small"
        >
          {filteredCategories.map(category => (
            <Panel
              key={category.id}
              header={
                <Space>
                  <span style={{ fontSize: '14px', fontWeight: 500 }}>
                    {category.name}
                  </span>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    ({category.nodes.length})
                  </Text>
                </Space>
              }
            >
              <div className="node-category-content">
                {category.nodes.map(nodeType => renderNodeItem(nodeType))}
              </div>
            </Panel>
          ))}
        </Collapse>
      </div>

      {filteredCategories.length === 0 && (
        <div className="node-panel-empty">
          <Text type="secondary">没有找到匹配的节点</Text>
        </div>
      )}
    </div>
  );
};

export default NodePanel;
