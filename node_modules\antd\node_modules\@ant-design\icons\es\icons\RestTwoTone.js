import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import RestTwoToneSvg from "@ant-design/icons-svg/es/asn/RestTwoTone";
import AntdIcon from "../components/AntdIcon";
var RestTwoTone = function RestTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: RestTwoToneSvg
  }));
};

/**![rest](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTMyNi40IDg0NGgzNjMuMmw0NC4zLTUyMEgyODJsNDQuNCA1MjB6TTUwOCA0MTZjNzkuNSAwIDE0NCA2NC41IDE0NCAxNDRzLTY0LjUgMTQ0LTE0NCAxNDQtMTQ0LTY0LjUtMTQ0LTE0NCA2NC41LTE0NCAxNDQtMTQ0eiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNNTA4IDcwNGM3OS41IDAgMTQ0LTY0LjUgMTQ0LTE0NHMtNjQuNS0xNDQtMTQ0LTE0NC0xNDQgNjQuNS0xNDQgMTQ0IDY0LjUgMTQ0IDE0NCAxNDR6bTAtMjI0YzQ0LjIgMCA4MCAzNS44IDgwIDgwcy0zNS44IDgwLTgwIDgwLTgwLTM1LjgtODAtODAgMzUuOC04MCA4MC04MHoiIGZpbGw9IiMxNjc3ZmYiIC8+PHBhdGggZD0iTTgzMiAyNTZoLTI4LjFsLTM1LjctMTIwLjljLTQtMTMuNy0xNi41LTIzLjEtMzAuNy0yMy4xaC00NTFjLTE0LjMgMC0yNi44IDkuNC0zMC43IDIzLjFMMjIwLjEgMjU2SDE5MmMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2MjhjMCA0LjQgMy42IDggOCA4aDQ1LjhsNDcuNyA1NTguN2EzMiAzMiAwIDAwMzEuOSAyOS4zaDQyOS4yYTMyIDMyIDAgMDAzMS45LTI5LjNMODAyLjIgMzI0SDg1NmM0LjQgMCA4LTMuNiA4LTh2LTI4YzAtMTcuNy0xNC4zLTMyLTMyLTMyem0tNTE4LjYtNzZoMzk3LjJsMjIuNCA3NkgyOTFsMjIuNC03NnptMzc2LjIgNjY0SDMyNi40TDI4MiAzMjRoNDUxLjlsLTQ0LjMgNTIweiIgZmlsbD0iIzE2NzdmZiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(RestTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'RestTwoTone';
}
export default RefIcon;