"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.useImage = exports.Portal = void 0;
__exportStar(require("./html"), exports);
var portal_1 = require("./portal");
Object.defineProperty(exports, "Portal", { enumerable: true, get: function () { return portal_1.Portal; } });
var use_image_1 = require("./use-image");
Object.defineProperty(exports, "useImage", { enumerable: true, get: function () { return use_image_1.useImage; } });
