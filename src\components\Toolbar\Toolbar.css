.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
  padding: 0 16px;
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.toolbar-center {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: center;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toolbar-logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.toolbar-logo::before {
  content: '';
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #1890ff, #722ed1);
  border-radius: 4px;
  display: inline-block;
}

/* 工具栏按钮样式 */
.toolbar .ant-btn {
  border: none;
  box-shadow: none;
  height: 32px;
  padding: 0 12px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.toolbar .ant-btn:hover {
  background-color: #f0f0f0;
}

.toolbar .ant-btn.ant-btn-primary {
  background: #1890ff;
  color: #fff;
}

.toolbar .ant-btn.ant-btn-primary:hover {
  background: #40a9ff;
}

.toolbar .ant-btn:disabled {
  color: #bfbfbf;
  background: transparent;
}

.toolbar .ant-btn:disabled:hover {
  background: transparent;
}

/* 分割线样式 */
.toolbar .ant-divider-vertical {
  height: 20px;
  margin: 0 8px;
  border-color: #e8e8e8;
}

/* 下拉菜单样式 */
.toolbar .ant-dropdown-menu {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e8e8e8;
}

.toolbar .ant-dropdown-menu-item {
  padding: 8px 16px;
  border-radius: 4px;
  margin: 4px;
}

.toolbar .ant-dropdown-menu-item:hover {
  background-color: #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .toolbar {
    padding: 0 12px;
  }
  
  .toolbar-left {
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .toolbar {
    padding: 0 8px;
  }
  
  .toolbar-center {
    display: none;
  }
  
  .toolbar-right .ant-typography {
    display: none;
  }
}
