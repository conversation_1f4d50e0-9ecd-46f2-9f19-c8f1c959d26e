import React, { useState, useEffect } from 'react';
import { Image as KonvaImage, Group, Rect } from 'react-konva';
import { BaseNode, ImageData } from '../../types';
import { getPreviewManager } from '../../utils/previewManager';
import { ImageProcessor } from '../../utils/imageProcessor';

interface NodePreviewProps {
  node: BaseNode;
  width: number;
  height: number;
  x?: number;
  y?: number;
}

const NodePreview: React.FC<NodePreviewProps> = ({ 
  node, 
  width, 
  height, 
  x = 0, 
  y = 0 
}) => {
  const [preview, setPreview] = useState<ImageData | null>(null);
  const [konvaImage, setKonvaImage] = useState<HTMLImageElement | HTMLCanvasElement | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const previewManager = getPreviewManager();
    if (!previewManager) return;

    // 注册预览回调
    const handlePreviewUpdate = (newPreview: ImageData | null) => {
      setPreview(newPreview);
      
      if (newPreview) {
        setKonvaImage(newPreview.data);
      } else {
        setKonvaImage(null);
      }
    };

    previewManager.registerPreviewCallback(node.id, handlePreviewUpdate);

    // 获取当前预览
    const currentPreview = previewManager.getPreview(node.id);
    handlePreviewUpdate(currentPreview);

    return () => {
      previewManager.unregisterPreviewCallback(node.id);
    };
  }, [node.id]);

  // 监听节点参数变化，触发预览更新
  useEffect(() => {
    const previewManager = getPreviewManager();
    if (!previewManager) return;

    setIsLoading(true);
    
    // 延迟更新预览，避免频繁更新
    const timer = setTimeout(() => {
      previewManager.updateNodePreview(node.id).finally(() => {
        setIsLoading(false);
      });
    }, 300);

    return () => {
      clearTimeout(timer);
      setIsLoading(false);
    };
  }, [node.parameters, node.id]);

  const renderPreviewContent = () => {
    if (isLoading) {
      return (
        <Group>
          <Rect
            x={x}
            y={y}
            width={width}
            height={height}
            fill="rgba(0, 0, 0, 0.1)"
            cornerRadius={4}
          />
          {/* 加载指示器 */}
          <Rect
            x={x + width / 2 - 10}
            y={y + height / 2 - 10}
            width={20}
            height={20}
            fill="#1890ff"
            cornerRadius={2}
            opacity={0.8}
          />
        </Group>
      );
    }

    if (!konvaImage) {
      return (
        <Group>
          <Rect
            x={x}
            y={y}
            width={width}
            height={height}
            fill="rgba(0, 0, 0, 0.05)"
            stroke="#d9d9d9"
            strokeWidth={1}
            dash={[5, 5]}
            cornerRadius={4}
          />
          {/* 无预览指示器 */}
          <Rect
            x={x + width / 2 - 8}
            y={y + height / 2 - 8}
            width={16}
            height={16}
            fill="#999"
            cornerRadius={2}
            opacity={0.5}
          />
        </Group>
      );
    }

    // 计算图像显示尺寸，保持宽高比
    const imageAspectRatio = preview!.width / preview!.height;
    const containerAspectRatio = width / height;
    
    let displayWidth = width;
    let displayHeight = height;
    let offsetX = 0;
    let offsetY = 0;

    if (imageAspectRatio > containerAspectRatio) {
      // 图像更宽，以宽度为准
      displayHeight = width / imageAspectRatio;
      offsetY = (height - displayHeight) / 2;
    } else {
      // 图像更高，以高度为准
      displayWidth = height * imageAspectRatio;
      offsetX = (width - displayWidth) / 2;
    }

    return (
      <Group>
        {/* 背景 */}
        <Rect
          x={x}
          y={y}
          width={width}
          height={height}
          fill="#f5f5f5"
          cornerRadius={4}
        />
        
        {/* 图像 */}
        <KonvaImage
          x={x + offsetX}
          y={y + offsetY}
          width={displayWidth}
          height={displayHeight}
          image={konvaImage}
          cornerRadius={4}
        />
        
        {/* 边框 */}
        <Rect
          x={x}
          y={y}
          width={width}
          height={height}
          stroke="#e8e8e8"
          strokeWidth={1}
          cornerRadius={4}
          listening={false}
        />
      </Group>
    );
  };

  return renderPreviewContent();
};

export default NodePreview;
