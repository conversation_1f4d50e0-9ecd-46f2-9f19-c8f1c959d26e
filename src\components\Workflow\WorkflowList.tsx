import React, { useState, useEffect } from 'react';
import {
  Modal,
  Table,
  Button,
  Space,
  Input,
  message,
  Popconfirm,
  Typography,
  Tag,
  Tooltip
} from 'antd';
import {
  SearchOutlined,
  EditOutlined,
  CopyOutlined,
  DeleteOutlined,
  DownloadOutlined,
  FolderOpenOutlined
} from '@ant-design/icons';
import { Workflow } from '../../types';
import { WorkflowManager } from '../../utils/workflowManager';
import { useAppStore } from '../../stores/useAppStore';

const { Text } = Typography;
const { Search } = Input;

interface WorkflowListProps {
  visible: boolean;
  onClose: () => void;
  mode: 'open' | 'manage';
}

const WorkflowList: React.FC<WorkflowListProps> = ({ visible, onClose, mode }) => {
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [filteredWorkflows, setFilteredWorkflows] = useState<Workflow[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  
  const { loadWorkflow } = useAppStore();

  // 加载工作流列表
  const loadWorkflows = () => {
    setLoading(true);
    try {
      const allWorkflows = WorkflowManager.getAllWorkflows();
      setWorkflows(allWorkflows);
      setFilteredWorkflows(allWorkflows);
    } catch (error) {
      message.error('加载工作流列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible) {
      loadWorkflows();
    }
  }, [visible]);

  // 搜索工作流
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (!query.trim()) {
      setFilteredWorkflows(workflows);
    } else {
      const filtered = WorkflowManager.searchWorkflows(query);
      setFilteredWorkflows(filtered);
    }
  };

  // 打开工作流
  const handleOpenWorkflow = (workflow: Workflow) => {
    try {
      loadWorkflow(workflow);
      message.success(`已打开工作流: ${workflow.name}`);
      onClose();
    } catch (error) {
      message.error('打开工作流失败');
    }
  };

  // 复制工作流
  const handleDuplicateWorkflow = async (workflow: Workflow) => {
    try {
      const duplicated = WorkflowManager.duplicateWorkflow(workflow.id);
      if (duplicated) {
        message.success(`已复制工作流: ${duplicated.name}`);
        loadWorkflows();
      } else {
        message.error('复制工作流失败');
      }
    } catch (error) {
      message.error('复制工作流失败');
    }
  };

  // 删除工作流
  const handleDeleteWorkflow = async (workflow: Workflow) => {
    try {
      const success = WorkflowManager.deleteWorkflow(workflow.id);
      if (success) {
        message.success(`已删除工作流: ${workflow.name}`);
        loadWorkflows();
      } else {
        message.error('删除工作流失败');
      }
    } catch (error) {
      message.error('删除工作流失败');
    }
  };

  // 导出工作流
  const handleExportWorkflow = (workflow: Workflow) => {
    try {
      WorkflowManager.exportWorkflow(workflow);
      message.success(`已导出工作流: ${workflow.name}`);
    } catch (error) {
      message.error('导出工作流失败');
    }
  };

  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      render: (name: string, record: Workflow) => (
        <div>
          <Text strong style={{ fontSize: '14px' }}>{name}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.nodes.length} 个节点 · {record.connections.length} 个连接
          </Text>
        </div>
      ),
    },
    {
      title: '修改时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 150,
      render: (date: Date) => (
        <Text type="secondary" style={{ fontSize: '12px' }}>
          {new Date(date).toLocaleString()}
        </Text>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: mode === 'open' ? 100 : 200,
      render: (_, record: Workflow) => (
        <Space size="small">
          {mode === 'open' ? (
            <Button
              type="primary"
              size="small"
              icon={<FolderOpenOutlined />}
              onClick={() => handleOpenWorkflow(record)}
            >
              打开
            </Button>
          ) : (
            <>
              <Tooltip title="打开">
                <Button
                  type="text"
                  size="small"
                  icon={<FolderOpenOutlined />}
                  onClick={() => handleOpenWorkflow(record)}
                />
              </Tooltip>
              
              <Tooltip title="复制">
                <Button
                  type="text"
                  size="small"
                  icon={<CopyOutlined />}
                  onClick={() => handleDuplicateWorkflow(record)}
                />
              </Tooltip>
              
              <Tooltip title="导出">
                <Button
                  type="text"
                  size="small"
                  icon={<DownloadOutlined />}
                  onClick={() => handleExportWorkflow(record)}
                />
              </Tooltip>
              
              <Popconfirm
                title="确定要删除这个工作流吗？"
                description="删除后无法恢复"
                onConfirm={() => handleDeleteWorkflow(record)}
                okText="删除"
                cancelText="取消"
                okType="danger"
              >
                <Tooltip title="删除">
                  <Button
                    type="text"
                    size="small"
                    icon={<DeleteOutlined />}
                    danger
                  />
                </Tooltip>
              </Popconfirm>
            </>
          )}
        </Space>
      ),
    },
  ];

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <span>📁</span>
          <span>{mode === 'open' ? '打开工作流' : '管理工作流'}</span>
        </div>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
      style={{ top: 20 }}
    >
      <div style={{ marginBottom: '16px' }}>
        <Search
          placeholder="搜索工作流..."
          allowClear
          value={searchQuery}
          onChange={(e) => handleSearch(e.target.value)}
          style={{ width: '100%' }}
        />
      </div>

      <Table
        columns={columns}
        dataSource={filteredWorkflows}
        rowKey="id"
        loading={loading}
        pagination={{
          pageSize: 10,
          showSizeChanger: false,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 个工作流`,
        }}
        locale={{
          emptyText: searchQuery ? '没有找到匹配的工作流' : '暂无工作流'
        }}
        size="small"
      />

      {workflows.length === 0 && !loading && (
        <div style={{ 
          textAlign: 'center', 
          padding: '40px 20px',
          color: '#999'
        }}>
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>📝</div>
          <div style={{ fontSize: '16px', marginBottom: '8px' }}>
            还没有工作流
          </div>
          <div style={{ fontSize: '14px' }}>
            创建您的第一个图像处理工作流吧！
          </div>
        </div>
      )}
    </Modal>
  );
};

export default WorkflowList;
