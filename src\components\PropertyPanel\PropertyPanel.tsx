import React from 'react';
import { 
  Card, 
  Typography, 
  Empty, 
  Divider,
  Space,
  Tag
} from 'antd';
import { useAppStore } from '../../stores/useAppStore';
import NodeProperties from './NodeProperties';
import './PropertyPanel.css';

const { Title, Text } = Typography;

const PropertyPanel: React.FC = () => {
  const { workflow, selectedNodes } = useAppStore();

  // 获取选中的节点
  const selectedNodeObjects = workflow.nodes.filter(node => 
    selectedNodes.includes(node.id)
  );

  const renderContent = () => {
    if (selectedNodes.length === 0) {
      return (
        <div className="property-panel-empty">
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="请选择一个节点来编辑属性"
          />
        </div>
      );
    }

    if (selectedNodes.length === 1) {
      const node = selectedNodeObjects[0];
      return (
        <div className="property-panel-content">
          <div className="property-panel-header">
            <Space direction="vertical" size={4}>
              <Title level={5} style={{ margin: 0 }}>
                {node.label}
              </Title>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                节点ID: {node.id.slice(0, 8)}...
              </Text>
              <Tag color="blue" style={{ fontSize: '11px' }}>
                {node.type}
              </Tag>
            </Space>
          </div>
          
          <Divider style={{ margin: '16px 0' }} />
          
          <NodeProperties node={node} />
        </div>
      );
    }

    // 多选状态
    return (
      <div className="property-panel-content">
        <div className="property-panel-header">
          <Title level={5} style={{ margin: 0 }}>
            多个节点已选中
          </Title>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            已选择 {selectedNodes.length} 个节点
          </Text>
        </div>
        
        <Divider style={{ margin: '16px 0' }} />
        
        <div className="selected-nodes-list">
          {selectedNodeObjects.map(node => (
            <Card
              key={node.id}
              size="small"
              className="selected-node-item"
            >
              <Space>
                <Text strong style={{ fontSize: '13px' }}>
                  {node.label}
                </Text>
                <Tag size="small" color="blue">
                  {node.type}
                </Tag>
              </Space>
            </Card>
          ))}
        </div>
        
        <div className="multi-select-actions">
          <Text type="secondary" style={{ fontSize: '12px' }}>
            批量操作功能即将推出
          </Text>
        </div>
      </div>
    );
  };

  return (
    <div className="property-panel">
      <div className="property-panel-title">
        <Title level={4} style={{ margin: 0 }}>
          属性面板
        </Title>
      </div>
      
      {renderContent()}
    </div>
  );
};

export default PropertyPanel;
