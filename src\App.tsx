import React, { useState } from 'react';
import { Layout } from 'antd';
import Toolbar from './components/Toolbar/Toolbar';
import NodePanel from './components/NodePanel/NodePanel';
import Canvas from './components/Canvas/Canvas';
import PropertyPanel from './components/PropertyPanel/PropertyPanel';
import StatusBar from './components/StatusBar/StatusBar';
import LayoutController from './components/Layout/LayoutController';
import './styles/App.css';

const { Header, Sider, Content, Footer } = Layout;

function App() {
  const [leftSiderCollapsed, setLeftSiderCollapsed] = useState(false);
  const [rightSiderCollapsed, setRightSiderCollapsed] = useState(false);

  const handleLeftSiderToggle = () => {
    setLeftSiderCollapsed(!leftSiderCollapsed);
  };

  const handleRightSiderToggle = () => {
    setRightSiderCollapsed(!rightSiderCollapsed);
  };

  return (
    <Layout className="app-layout">
      {/* 顶部工具栏 */}
      <Header className="app-header">
        <Toolbar />
      </Header>

      <Layout className="app-content">
        {/* 左侧节点面板 */}
        <Sider
          width={280}
          className={`app-sider-left ${leftSiderCollapsed ? 'app-sider-collapsed' : ''}`}
          theme="light"
          collapsed={leftSiderCollapsed}
          collapsedWidth={0}
          trigger={null}
        >
          <NodePanel />
        </Sider>

        {/* 中央画布区域 */}
        <Content className="app-main-content">
          <Canvas />

          {/* 布局控制器 */}
          <LayoutController
            leftSiderCollapsed={leftSiderCollapsed}
            rightSiderCollapsed={rightSiderCollapsed}
            onLeftSiderToggle={handleLeftSiderToggle}
            onRightSiderToggle={handleRightSiderToggle}
          />
        </Content>

        {/* 右侧属性面板 */}
        <Sider
          width={320}
          className={`app-sider-right ${rightSiderCollapsed ? 'app-sider-collapsed' : ''}`}
          theme="light"
          collapsed={rightSiderCollapsed}
          collapsedWidth={0}
          trigger={null}
        >
          <PropertyPanel />
        </Sider>
      </Layout>

      {/* 底部状态栏 */}
      <Footer className="app-footer">
        <StatusBar />
      </Footer>
    </Layout>
  );
}

export default App;
