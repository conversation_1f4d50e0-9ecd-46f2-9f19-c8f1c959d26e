import React from 'react';
import { Layout } from 'antd';
import Toolbar from './components/Toolbar/Toolbar';
import NodePanel from './components/NodePanel/NodePanel';
import Canvas from './components/Canvas/Canvas';
import PropertyPanel from './components/PropertyPanel/PropertyPanel';
import StatusBar from './components/StatusBar/StatusBar';
import './styles/App.css';

const { Header, Sider, Content, Footer } = Layout;

function App() {
  return (
    <Layout className="app-layout">
      {/* 顶部工具栏 */}
      <Header className="app-header">
        <Toolbar />
      </Header>

      <Layout className="app-content">
        {/* 左侧节点面板 */}
        <Sider
          width={280}
          className="app-sider-left"
          theme="light"
        >
          <NodePanel />
        </Sider>

        {/* 中央画布区域 */}
        <Content className="app-main-content">
          <Canvas />
        </Content>

        {/* 右侧属性面板 */}
        <Sider
          width={320}
          className="app-sider-right"
          theme="light"
        >
          <PropertyPanel />
        </Sider>
      </Layout>

      {/* 底部状态栏 */}
      <Footer className="app-footer">
        <StatusBar />
      </Footer>
    </Layout>
  );
}

export default App;
