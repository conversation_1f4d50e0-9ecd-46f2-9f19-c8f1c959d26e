import React from 'react';
import {
  Form,
  InputNumber,
  Switch,
  Select,
  Slider,
  ColorPicker,
  Input,
  Space,
  Typography,
  Card
} from 'antd';
import { BaseNode } from '../../types';
import { useAppStore } from '../../stores/useAppStore';

const { Text } = Typography;
const { Option } = Select;

interface NodePropertiesProps {
  node: BaseNode;
}

const NodeProperties: React.FC<NodePropertiesProps> = ({ node }) => {
  const { updateNode } = useAppStore();
  const [form] = Form.useForm();

  const handleValueChange = (changedValues: any) => {
    updateNode(node.id, {
      parameters: {
        ...node.parameters,
        ...changedValues
      }
    });
  };

  const renderParameterControl = (paramName: string, paramValue: any, paramType: string) => {
    switch (paramType) {
      case 'number':
        return (
          <InputNumber
            value={paramValue}
            onChange={(value) => handleValueChange({ [paramName]: value })}
            style={{ width: '100%' }}
          />
        );
      
      case 'range':
        return (
          <Space direction="vertical" style={{ width: '100%' }}>
            <Slider
              value={paramValue}
              onChange={(value) => handleValueChange({ [paramName]: value })}
              min={getParameterMin(node.type, paramName)}
              max={getParameterMax(node.type, paramName)}
              step={getParameterStep(node.type, paramName)}
            />
            <InputNumber
              value={paramValue}
              onChange={(value) => handleValueChange({ [paramName]: value })}
              min={getParameterMin(node.type, paramName)}
              max={getParameterMax(node.type, paramName)}
              step={getParameterStep(node.type, paramName)}
              size="small"
              style={{ width: '80px' }}
            />
          </Space>
        );
      
      case 'boolean':
        return (
          <Switch
            checked={paramValue}
            onChange={(checked) => handleValueChange({ [paramName]: checked })}
          />
        );
      
      case 'select':
        const options = getParameterOptions(node.type, paramName);
        return (
          <Select
            value={paramValue}
            onChange={(value) => handleValueChange({ [paramName]: value })}
            style={{ width: '100%' }}
          >
            {options.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        );
      
      case 'color':
        return (
          <ColorPicker
            value={paramValue}
            onChange={(color) => handleValueChange({ [paramName]: color.toHexString() })}
            showText
          />
        );
      
      case 'string':
        return (
          <Input
            value={paramValue}
            onChange={(e) => handleValueChange({ [paramName]: e.target.value })}
          />
        );
      
      default:
        return (
          <Input
            value={paramValue}
            onChange={(e) => handleValueChange({ [paramName]: e.target.value })}
          />
        );
    }
  };

  const getNodeParameterDefinitions = (nodeType: string) => {
    switch (nodeType) {
      case 'resize':
        return [
          { name: 'width', label: '宽度', type: 'number' },
          { name: 'height', label: '高度', type: 'number' },
          { name: 'maintainAspectRatio', label: '保持宽高比', type: 'boolean' }
        ];
      
      case 'brightness-contrast':
        return [
          { name: 'brightness', label: '亮度', type: 'range' },
          { name: 'contrast', label: '对比度', type: 'range' }
        ];
      
      case 'hue-saturation':
        return [
          { name: 'hue', label: '色调', type: 'range' },
          { name: 'saturation', label: '饱和度', type: 'range' },
          { name: 'lightness', label: '明度', type: 'range' }
        ];
      
      case 'blur':
        return [
          { name: 'radius', label: '模糊半径', type: 'range' },
          { name: 'type', label: '模糊类型', type: 'select' }
        ];
      
      case 'rotate':
        return [
          { name: 'angle', label: '旋转角度', type: 'range' },
          { name: 'backgroundColor', label: '背景颜色', type: 'color' }
        ];
      
      case 'crop':
        return [
          { name: 'x', label: 'X坐标', type: 'number' },
          { name: 'y', label: 'Y坐标', type: 'number' },
          { name: 'width', label: '宽度', type: 'number' },
          { name: 'height', label: '高度', type: 'number' }
        ];
      
      default:
        return [];
    }
  };

  const getParameterMin = (nodeType: string, paramName: string): number => {
    const ranges: Record<string, Record<string, number>> = {
      'brightness-contrast': { brightness: -100, contrast: -100 },
      'hue-saturation': { hue: -180, saturation: -100, lightness: -100 },
      'blur': { radius: 0 },
      'rotate': { angle: -360 }
    };
    return ranges[nodeType]?.[paramName] ?? 0;
  };

  const getParameterMax = (nodeType: string, paramName: string): number => {
    const ranges: Record<string, Record<string, number>> = {
      'brightness-contrast': { brightness: 100, contrast: 100 },
      'hue-saturation': { hue: 180, saturation: 100, lightness: 100 },
      'blur': { radius: 50 },
      'rotate': { angle: 360 }
    };
    return ranges[nodeType]?.[paramName] ?? 100;
  };

  const getParameterStep = (nodeType: string, paramName: string): number => {
    const steps: Record<string, Record<string, number>> = {
      'brightness-contrast': { brightness: 1, contrast: 1 },
      'hue-saturation': { hue: 1, saturation: 1, lightness: 1 },
      'blur': { radius: 0.1 },
      'rotate': { angle: 1 }
    };
    return steps[nodeType]?.[paramName] ?? 1;
  };

  const getParameterOptions = (nodeType: string, paramName: string) => {
    const options: Record<string, Record<string, Array<{ label: string; value: any }>>> = {
      'blur': {
        type: [
          { label: '高斯模糊', value: 'gaussian' },
          { label: '运动模糊', value: 'motion' },
          { label: '径向模糊', value: 'radial' }
        ]
      }
    };
    return options[nodeType]?.[paramName] ?? [];
  };

  const parameterDefinitions = getNodeParameterDefinitions(node.type);

  if (parameterDefinitions.length === 0) {
    return (
      <div className="node-properties-empty">
        <Text type="secondary">此节点没有可配置的参数</Text>
      </div>
    );
  }

  return (
    <div className="node-properties">
      <Form
        form={form}
        layout="vertical"
        size="small"
        initialValues={node.parameters}
      >
        {parameterDefinitions.map(param => (
          <Card
            key={param.name}
            size="small"
            className="parameter-card"
            title={param.label}
          >
            <Form.Item name={param.name} style={{ margin: 0 }}>
              {renderParameterControl(
                param.name,
                node.parameters[param.name],
                param.type
              )}
            </Form.Item>
          </Card>
        ))}
      </Form>
      
      <div className="node-info">
        <Text type="secondary" style={{ fontSize: '11px' }}>
          输入: {node.inputs.length} | 输出: {node.outputs.length}
        </Text>
      </div>
    </div>
  );
};

export default NodeProperties;
