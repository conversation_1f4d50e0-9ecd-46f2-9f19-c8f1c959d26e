/**
 * 键盘快捷键管理器
 */
export class KeyboardShortcuts {
  private shortcuts: Map<string, () => void> = new Map();
  private isEnabled = true;

  constructor() {
    this.bindEvents();
  }

  /**
   * 绑定键盘事件
   */
  private bindEvents() {
    document.addEventListener('keydown', this.handleKeyDown.bind(this));
  }

  /**
   * 处理键盘按下事件
   */
  private handleKeyDown(event: KeyboardEvent) {
    if (!this.isEnabled) return;

    // 忽略在输入框中的按键
    const target = event.target as HTMLElement;
    if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true') {
      return;
    }

    const key = this.getKeyString(event);
    const handler = this.shortcuts.get(key);
    
    if (handler) {
      event.preventDefault();
      handler();
    }
  }

  /**
   * 获取按键字符串
   */
  private getKeyString(event: KeyboardEvent): string {
    const parts: string[] = [];
    
    if (event.ctrlKey || event.metaKey) parts.push('ctrl');
    if (event.altKey) parts.push('alt');
    if (event.shiftKey) parts.push('shift');
    
    parts.push(event.key.toLowerCase());
    
    return parts.join('+');
  }

  /**
   * 注册快捷键
   */
  register(key: string, handler: () => void) {
    this.shortcuts.set(key.toLowerCase(), handler);
  }

  /**
   * 注销快捷键
   */
  unregister(key: string) {
    this.shortcuts.delete(key.toLowerCase());
  }

  /**
   * 启用快捷键
   */
  enable() {
    this.isEnabled = true;
  }

  /**
   * 禁用快捷键
   */
  disable() {
    this.isEnabled = false;
  }

  /**
   * 清除所有快捷键
   */
  clear() {
    this.shortcuts.clear();
  }

  /**
   * 获取所有已注册的快捷键
   */
  getShortcuts(): Array<{ key: string; description: string }> {
    return [
      { key: 'ctrl+n', description: '新建工作流' },
      { key: 'ctrl+o', description: '打开工作流' },
      { key: 'ctrl+s', description: '保存工作流' },
      { key: 'ctrl+z', description: '撤销' },
      { key: 'ctrl+y', description: '重做' },
      { key: 'ctrl+shift+z', description: '重做' },
      { key: 'delete', description: '删除选中节点' },
      { key: 'backspace', description: '删除选中节点' },
      { key: 'ctrl+a', description: '全选节点' },
      { key: 'ctrl+c', description: '复制节点' },
      { key: 'ctrl+v', description: '粘贴节点' },
      { key: 'ctrl+x', description: '剪切节点' },
      { key: 'ctrl+d', description: '复制节点' },
      { key: 'escape', description: '取消选择' },
      { key: 'space', description: '平移画布' },
      { key: 'ctrl+=', description: '放大画布' },
      { key: 'ctrl+-', description: '缩小画布' },
      { key: 'ctrl+0', description: '重置画布缩放' },
      { key: 'ctrl+1', description: '适应画布' },
      { key: 'f11', description: '全屏切换' },
      { key: 'ctrl+/', description: '显示快捷键帮助' },
      { key: 'tab', description: '切换面板' },
      { key: 'ctrl+enter', description: '执行工作流' }
    ];
  }

  /**
   * 销毁快捷键管理器
   */
  destroy() {
    document.removeEventListener('keydown', this.handleKeyDown.bind(this));
    this.clear();
  }
}

/**
 * 全局快捷键管理器实例
 */
export const globalShortcuts = new KeyboardShortcuts();

/**
 * 初始化应用快捷键
 */
export const initializeShortcuts = (callbacks: {
  newWorkflow: () => void;
  openWorkflow: () => void;
  saveWorkflow: () => void;
  undo: () => void;
  redo: () => void;
  deleteSelected: () => void;
  selectAll: () => void;
  copy: () => void;
  paste: () => void;
  cut: () => void;
  duplicate: () => void;
  clearSelection: () => void;
  zoomIn: () => void;
  zoomOut: () => void;
  resetZoom: () => void;
  fitToScreen: () => void;
  toggleFullscreen: () => void;
  showHelp: () => void;
  executeWorkflow: () => void;
}) => {
  // 文件操作
  globalShortcuts.register('ctrl+n', callbacks.newWorkflow);
  globalShortcuts.register('ctrl+o', callbacks.openWorkflow);
  globalShortcuts.register('ctrl+s', callbacks.saveWorkflow);

  // 编辑操作
  globalShortcuts.register('ctrl+z', callbacks.undo);
  globalShortcuts.register('ctrl+y', callbacks.redo);
  globalShortcuts.register('ctrl+shift+z', callbacks.redo);

  // 节点操作
  globalShortcuts.register('delete', callbacks.deleteSelected);
  globalShortcuts.register('backspace', callbacks.deleteSelected);
  globalShortcuts.register('ctrl+a', callbacks.selectAll);
  globalShortcuts.register('ctrl+c', callbacks.copy);
  globalShortcuts.register('ctrl+v', callbacks.paste);
  globalShortcuts.register('ctrl+x', callbacks.cut);
  globalShortcuts.register('ctrl+d', callbacks.duplicate);
  globalShortcuts.register('escape', callbacks.clearSelection);

  // 视图操作
  globalShortcuts.register('ctrl+=', callbacks.zoomIn);
  globalShortcuts.register('ctrl+-', callbacks.zoomOut);
  globalShortcuts.register('ctrl+0', callbacks.resetZoom);
  globalShortcuts.register('ctrl+1', callbacks.fitToScreen);
  globalShortcuts.register('f11', callbacks.toggleFullscreen);

  // 其他操作
  globalShortcuts.register('ctrl+/', callbacks.showHelp);
  globalShortcuts.register('ctrl+enter', callbacks.executeWorkflow);
};

/**
 * 清理快捷键
 */
export const cleanupShortcuts = () => {
  globalShortcuts.destroy();
};
