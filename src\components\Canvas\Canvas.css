.canvas-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  overflow: hidden;
  cursor: grab;
}

.canvas-container:active {
  cursor: grabbing;
}

.canvas-container.drag-over {
  background: rgba(24, 144, 255, 0.05);
  border: 2px dashed #1890ff;
}

/* 画布信息显示 */
.canvas-info {
  position: absolute;
  bottom: 16px;
  left: 16px;
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  display: flex;
  gap: 16px;
  pointer-events: none;
  z-index: 10;
}

.canvas-info span {
  white-space: nowrap;
}

/* 网格样式 */
.canvas-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  opacity: 0.5;
}

/* 选择框样式 */
.selection-box {
  position: absolute;
  border: 2px dashed #1890ff;
  background: rgba(24, 144, 255, 0.1);
  pointer-events: none;
  z-index: 5;
}

/* 连接线预览 */
.connection-preview {
  position: absolute;
  pointer-events: none;
  z-index: 15;
}

.connection-preview line {
  stroke: #1890ff;
  stroke-width: 2;
  stroke-dasharray: 5, 5;
  animation: dash 1s linear infinite;
}

@keyframes dash {
  to {
    stroke-dashoffset: -10;
  }
}

/* 拖拽提示 */
.drop-hint {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(24, 144, 255, 0.9);
  color: #fff;
  padding: 16px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  pointer-events: none;
  z-index: 20;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.canvas-container.drag-over .drop-hint {
  opacity: 1;
}

/* 缩放控制 */
.zoom-controls {
  position: absolute;
  top: 16px;
  right: 16px;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  z-index: 10;
}

.zoom-controls button {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #666;
  transition: all 0.2s ease;
}

.zoom-controls button:hover {
  background: #f0f0f0;
  color: #1890ff;
}

.zoom-controls button:first-child {
  border-radius: 6px 6px 0 0;
}

.zoom-controls button:last-child {
  border-radius: 0 0 6px 6px;
}

.zoom-controls button:not(:last-child) {
  border-bottom: 1px solid #e8e8e8;
}

/* 小地图 */
.minimap {
  position: absolute;
  bottom: 16px;
  right: 16px;
  width: 200px;
  height: 150px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  overflow: hidden;
  z-index: 10;
}

.minimap-viewport {
  position: absolute;
  border: 2px solid #1890ff;
  background: rgba(24, 144, 255, 0.1);
  cursor: move;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .canvas-info {
    bottom: 8px;
    left: 8px;
    font-size: 11px;
    gap: 8px;
    padding: 6px 8px;
  }
  
  .zoom-controls {
    top: 8px;
    right: 8px;
  }
  
  .minimap {
    display: none;
  }
}

/* 性能优化 */
.canvas-container * {
  will-change: transform;
}

/* 禁用选择 */
.canvas-container {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
