.status-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 32px;
  padding: 0 16px;
  background: #fff;
  border-top: 1px solid #e8e8e8;
  font-size: 12px;
  color: #666;
}

.status-bar-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.status-bar-center {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.status-bar-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex: 1;
}

/* 状态指示器 */
.status-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-indicator.processing {
  color: #1890ff;
}

.status-indicator.ready {
  color: #52c41a;
}

.status-indicator.error {
  color: #ff4d4f;
}

.status-indicator.warning {
  color: #faad14;
}

/* 进度条样式 */
.status-bar .ant-progress {
  margin: 0;
}

.status-bar .ant-progress-line {
  font-size: 12px;
}

.status-bar .ant-progress-bg {
  height: 4px !important;
  border-radius: 2px;
}

.status-bar .ant-progress-inner {
  background: #f0f0f0;
  border-radius: 2px;
}

/* 标签样式 */
.status-bar .ant-tag {
  margin: 0;
  font-size: 11px;
  line-height: 1.2;
  padding: 1px 4px;
  border-radius: 2px;
}

/* 文本样式 */
.status-bar .ant-typography {
  margin: 0;
  line-height: 1;
}

/* 分隔符 */
.status-separator {
  width: 1px;
  height: 16px;
  background: #e8e8e8;
  margin: 0 8px;
}

/* 性能指示器 */
.performance-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
}

.memory-usage {
  display: flex;
  align-items: center;
  gap: 4px;
}

.memory-usage.high {
  color: #ff4d4f;
}

.memory-usage.medium {
  color: #faad14;
}

.memory-usage.low {
  color: #52c41a;
}

/* 工具提示样式 */
.status-bar .ant-tooltip {
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .status-bar {
    padding: 0 12px;
  }
  
  .status-bar-center {
    display: none;
  }
}

@media (max-width: 768px) {
  .status-bar {
    padding: 0 8px;
    font-size: 11px;
  }
  
  .status-bar-right .performance-indicator {
    display: none;
  }
  
  .status-bar-right {
    gap: 8px;
  }
}

/* 动画效果 */
.status-loading {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* 状态变化动画 */
.status-change {
  animation: statusChange 0.3s ease-out;
}

@keyframes statusChange {
  0% {
    transform: scale(0.95);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 错误状态样式 */
.status-error {
  background: #fff2f0;
  border-top-color: #ffccc7;
  color: #a8071a;
}

.status-error .status-indicator {
  color: #ff4d4f;
}

/* 警告状态样式 */
.status-warning {
  background: #fffbe6;
  border-top-color: #ffe58f;
  color: #ad6800;
}

.status-warning .status-indicator {
  color: #faad14;
}

/* 成功状态样式 */
.status-success {
  background: #f6ffed;
  border-top-color: #b7eb8f;
  color: #135200;
}

.status-success .status-indicator {
  color: #52c41a;
}
