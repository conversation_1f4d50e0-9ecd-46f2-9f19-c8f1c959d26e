import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import RedditCircleFilledSvg from "@ant-design/icons-svg/es/asn/RedditCircleFilled";
import AntdIcon from "../components/AntdIcon";
var RedditCircleFilled = function RedditCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: RedditCircleFilledSvg
  }));
};

/**![reddit-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTU4NCA1NDhhMzYgMzYgMCAxMDcyIDAgMzYgMzYgMCAxMC03MiAwem0xNDQtMTA4YTM1LjkgMzUuOSAwIDAwLTMyLjUgMjAuNmMxOC44IDE0LjMgMzQuNCAzMC43IDQ1LjkgNDguOEEzNS45OCAzNS45OCAwIDAwNzI4IDQ0MHpNNTEyIDY0QzI2NC42IDY0IDY0IDI2NC42IDY0IDUxMnMyMDAuNiA0NDggNDQ4IDQ0OCA0NDgtMjAwLjYgNDQ4LTQ0OFM3NTkuNCA2NCA1MTIgNjR6bTI0NSA0NzcuOWM0LjYgMTMuNSA3IDI3LjYgNyA0Mi4xIDAgOTkuNC0xMTIuOCAxODAtMjUyIDE4MHMtMjUyLTgwLjYtMjUyLTE4MGMwLTE0LjUgMi40LTI4LjYgNy00Mi4xQTcyLjAxIDcyLjAxIDAgMDEyOTYgNDA0YzI3LjEgMCA1MC42IDE0LjkgNjIuOSAzNyAzNi4yLTE5LjggODAuMi0zMi44IDEyOC4xLTM2LjFsNTguNC0xMzEuMWM0LjMtOS44IDE1LjItMTQuOCAyNS41LTExLjhsOTEuNiAyNi41YTU0LjAzIDU0LjAzIDAgMDExMDEuNiAyNS42YzAgMjkuOC0yNC4yIDU0LTU0IDU0LTIzLjUgMC00My41LTE1LjEtNTAuOS0zNi4xTDU3NyAzMDguM2wtNDMgOTYuNWM0OS4xIDMgOTQuMiAxNi4xIDEzMS4yIDM2LjMgMTIuMy0yMi4xIDM1LjgtMzcgNjIuOS0zNyAzOS44IDAgNzIgMzIuMiA3MiA3Mi0uMSAyOS4zLTE3LjggNTQuNi00My4xIDY1Ljh6bS0xNzEuMyA4M2MtMTQuOSAxMS43LTQ0LjMgMjQuMy03My43IDI0LjNzLTU4LjktMTIuNi03My43LTI0LjNjLTkuMy03LjMtMjIuNy01LjctMzAgMy42LTcuMyA5LjMtNS43IDIyLjcgMy42IDMwIDI1LjcgMjAuNCA2NSAzMy41IDEwMC4xIDMzLjUgMzUuMSAwIDc0LjQtMTMuMSAxMDAuMi0zMy41IDkuMy03LjMgMTAuOS0yMC44IDMuNi0zMGEyMS40NiAyMS40NiAwIDAwLTMwLjEtMy42ek0yOTYgNDQwYTM1Ljk4IDM1Ljk4IDAgMDAtMTMuNCA2OS40YzExLjUtMTguMSAyNy4xLTM0LjUgNDUuOS00OC44QTM1LjkgMzUuOSAwIDAwMjk2IDQ0MHptNzIgMTA4YTM2IDM2IDAgMTA3MiAwIDM2IDM2IDAgMTAtNzIgMHoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(RedditCircleFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'RedditCircleFilled';
}
export default RefIcon;