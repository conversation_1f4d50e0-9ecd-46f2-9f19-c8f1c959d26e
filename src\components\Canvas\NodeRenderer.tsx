import React from 'react';
import { Group, Rect, Text, Circle } from 'react-konva';
import { Html } from 'react-konva-utils';
import { BaseNode } from '../../types';
import { useAppStore } from '../../stores/useAppStore';
import ImageUploadNode from '../Nodes/ImageUploadNode';
import NodePreview from './NodePreview';

interface NodeRendererProps {
  node: BaseNode;
  onConnectionDragStart?: (
    node: BaseNode,
    connection: any,
    position: { x: number; y: number }
  ) => void;
}

const NodeRenderer: React.FC<NodeRendererProps> = ({ node, onConnectionDragStart }) => {
  const { selectNode, updateNode, selectedNodes } = useAppStore();
  
  const isSelected = selectedNodes.includes(node.id);
  
  // 节点颜色配置
  const getNodeColor = (nodeType: string) => {
    const colors: Record<string, string> = {
      'image-upload': '#52c41a',
      'resize': '#1890ff',
      'rotate': '#1890ff',
      'crop': '#1890ff',
      'flip': '#1890ff',
      'brightness-contrast': '#fa8c16',
      'hue-saturation': '#fa8c16',
      'color-balance': '#fa8c16',
      'curves': '#fa8c16',
      'levels': '#fa8c16',
      'blur': '#722ed1',
      'sharpen': '#722ed1',
      'noise': '#722ed1',
      'edge-detection': '#722ed1',
      'emboss': '#722ed1',
      'image-export': '#f5222d'
    };
    return colors[nodeType] || '#666';
  };

  const handleClick = (e: any) => {
    e.cancelBubble = true;
    selectNode(node.id, e.evt.ctrlKey || e.evt.metaKey);
  };

  const handleDragStart = () => {
    if (!isSelected) {
      selectNode(node.id);
    }
  };

  const handleDragEnd = (e: any) => {
    updateNode(node.id, {
      position: {
        x: e.target.x(),
        y: e.target.y()
      }
    });
  };

  const handleConnectionMouseDown = (
    connection: any,
    type: 'input' | 'output',
    e: any
  ) => {
    e.cancelBubble = true;

    if (type === 'output' && onConnectionDragStart) {
      const stage = e.target.getStage();
      const pointerPosition = stage.getPointerPosition();
      onConnectionDragStart(node, connection, pointerPosition);
    }
  };

  const nodeColor = getNodeColor(node.type);
  const headerHeight = 24;
  const connectionRadius = 6;
  const connectionSpacing = 20;
  const previewHeight = 60; // 预览区域高度

  // 渲染节点内容
  const renderNodeContent = () => {
    switch (node.type) {
      case 'image-upload':
        return (
          <Html
            divProps={{
              style: {
                position: 'absolute',
                top: headerHeight,
                left: 0,
                width: node.size.width,
                height: node.size.height - headerHeight,
                pointerEvents: 'auto'
              }
            }}
          >
            <ImageUploadNode node={node} />
          </Html>
        );
      default:
        return null;
    }
  };

  return (
    <Group
      x={node.position.x}
      y={node.position.y}
      draggable
      onClick={handleClick}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      {/* 选中状态的外框 */}
      {isSelected && (
        <Rect
          x={-2}
          y={-2}
          width={node.size.width + 4}
          height={node.size.height + 4}
          stroke="#1890ff"
          strokeWidth={2}
          cornerRadius={6}
          dash={[5, 5]}
        />
      )}
      
      {/* 节点主体 */}
      <Rect
        width={node.size.width}
        height={node.size.height}
        fill="#fff"
        stroke={isSelected ? nodeColor : '#d9d9d9'}
        strokeWidth={isSelected ? 2 : 1}
        cornerRadius={4}
        shadowColor="rgba(0, 0, 0, 0.1)"
        shadowBlur={4}
        shadowOffset={{ x: 0, y: 2 }}
      />
      
      {/* 节点头部 */}
      <Rect
        width={node.size.width}
        height={headerHeight}
        fill={nodeColor}
        cornerRadius={[4, 4, 0, 0]}
      />
      
      {/* 节点标题 */}
      <Text
        x={8}
        y={6}
        text={node.label}
        fontSize={12}
        fontFamily="Arial"
        fill="#fff"
        width={node.size.width - 16}
        ellipsis={true}
      />
      
      {/* 输入连接点 */}
      {node.inputs.map((input, index) => (
        <Group key={input.id}>
          <Circle
            x={0}
            y={headerHeight + 16 + index * connectionSpacing}
            radius={connectionRadius}
            fill="#fff"
            stroke={getConnectionColor(input.dataType)}
            strokeWidth={2}
          />
          <Text
            x={connectionRadius + 8}
            y={headerHeight + 12 + index * connectionSpacing}
            text={input.label}
            fontSize={10}
            fontFamily="Arial"
            fill="#666"
          />
        </Group>
      ))}
      
      {/* 输出连接点 */}
      {node.outputs.map((output, index) => (
        <Group key={output.id}>
          <Circle
            x={node.size.width}
            y={headerHeight + 16 + index * connectionSpacing}
            radius={connectionRadius}
            fill="#fff"
            stroke={getConnectionColor(output.dataType)}
            strokeWidth={2}
            onMouseDown={(e) => handleConnectionMouseDown(output, 'output', e)}
            style={{ cursor: 'crosshair' }}
          />
          <Text
            x={node.size.width - connectionRadius - 8}
            y={headerHeight + 12 + index * connectionSpacing}
            text={output.label}
            fontSize={10}
            fontFamily="Arial"
            fill="#666"
            align="right"
            width={node.size.width - connectionRadius - 16}
          />
        </Group>
      ))}
      
      {/* 节点状态指示器 */}
      <Circle
        x={node.size.width - 8}
        y={8}
        radius={3}
        fill={getNodeStatusColor(node)}
      />

      {/* 节点内容 */}
      {renderNodeContent()}

      {/* 节点预览 */}
      {node.type !== 'image-upload' && (
        <NodePreview
          node={node}
          width={node.size.width - 16}
          height={previewHeight}
          x={8}
          y={node.size.height - previewHeight - 8}
        />
      )}
    </Group>
  );
};

// 获取连接点颜色
const getConnectionColor = (dataType: string): string => {
  const colors: Record<string, string> = {
    'image': '#52c41a',
    'number': '#1890ff',
    'color': '#fa8c16',
    'boolean': '#722ed1'
  };
  return colors[dataType] || '#666';
};

// 获取节点状态颜色
const getNodeStatusColor = (node: BaseNode): string => {
  // 这里可以根据节点的处理状态返回不同颜色
  // 绿色: 正常, 黄色: 警告, 红色: 错误, 灰色: 未连接
  
  const hasInputs = node.inputs.length === 0 || node.inputs.some(input => {
    // 检查是否有连接到此输入的连接线
    // 这里需要从store中获取连接信息
    return true; // 临时返回true
  });
  
  if (!hasInputs) return '#d9d9d9'; // 灰色 - 未连接
  return '#52c41a'; // 绿色 - 正常
};

export default NodeRenderer;
