"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _QuestionCircleFilled = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/QuestionCircleFilled"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var QuestionCircleFilled = function QuestionCircleFilled(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _QuestionCircleFilled.default
  }));
};

/**![question-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0wIDcwOGMtMjIuMSAwLTQwLTE3LjktNDAtNDBzMTcuOS00MCA0MC00MCA0MCAxNy45IDQwIDQwLTE3LjkgNDAtNDAgNDB6bTYyLjktMjE5LjVhNDguMyA0OC4zIDAgMDAtMzAuOSA0NC44VjYyMGMwIDQuNC0zLjYgOC04IDhoLTQ4Yy00LjQgMC04LTMuNi04LTh2LTIxLjVjMC0yMy4xIDYuNy00NS45IDE5LjktNjQuOSAxMi45LTE4LjYgMzAuOS0zMi44IDUyLjEtNDAuOSAzNC0xMy4xIDU2LTQxLjYgNTYtNzIuNyAwLTQ0LjEtNDMuMS04MC05Ni04MHMtOTYgMzUuOS05NiA4MHY3LjZjMCA0LjQtMy42IDgtOCA4aC00OGMtNC40IDAtOC0zLjYtOC04VjQyMGMwLTM5LjMgMTcuMi03NiA0OC40LTEwMy4zQzQzMC40IDI5MC40IDQ3MCAyNzYgNTEyIDI3NnM4MS42IDE0LjUgMTExLjYgNDAuN0M2NTQuOCAzNDQgNjcyIDM4MC43IDY3MiA0MjBjMCA1Ny44LTM4LjEgMTA5LjgtOTcuMSAxMzIuNXoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(QuestionCircleFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'QuestionCircleFilled';
}
var _default = exports.default = RefIcon;