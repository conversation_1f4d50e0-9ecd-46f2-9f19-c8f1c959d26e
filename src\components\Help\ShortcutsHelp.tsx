import React from 'react';
import { Modal, Table, Typography, Tag, Divider } from 'antd';
import { globalShortcuts } from '../../utils/keyboardShortcuts';

const { Title, Text } = Typography;

interface ShortcutsHelpProps {
  visible: boolean;
  onClose: () => void;
}

const ShortcutsHelp: React.FC<ShortcutsHelpProps> = ({ visible, onClose }) => {
  const shortcuts = globalShortcuts.getShortcuts();

  // 按类别分组快捷键
  const groupedShortcuts = {
    file: shortcuts.filter(s => ['ctrl+n', 'ctrl+o', 'ctrl+s'].includes(s.key)),
    edit: shortcuts.filter(s => ['ctrl+z', 'ctrl+y', 'ctrl+shift+z', 'delete', 'backspace', 'ctrl+a', 'ctrl+c', 'ctrl+v', 'ctrl+x', 'ctrl+d', 'escape'].includes(s.key)),
    view: shortcuts.filter(s => ['ctrl+=', 'ctrl+-', 'ctrl+0', 'ctrl+1', 'f11', 'space'].includes(s.key)),
    workflow: shortcuts.filter(s => ['ctrl+enter', 'tab'].includes(s.key)),
    help: shortcuts.filter(s => ['ctrl+/'].includes(s.key))
  };

  const renderShortcutKey = (key: string) => {
    const parts = key.split('+');
    return (
      <div style={{ display: 'flex', gap: '4px', alignItems: 'center' }}>
        {parts.map((part, index) => (
          <React.Fragment key={index}>
            <Tag 
              style={{ 
                margin: 0,
                fontFamily: 'monospace',
                fontSize: '11px',
                padding: '2px 6px',
                background: '#f5f5f5',
                border: '1px solid #d9d9d9',
                borderRadius: '3px'
              }}
            >
              {part.charAt(0).toUpperCase() + part.slice(1)}
            </Tag>
            {index < parts.length - 1 && <Text type="secondary">+</Text>}
          </React.Fragment>
        ))}
      </div>
    );
  };

  const renderShortcutGroup = (title: string, shortcuts: Array<{ key: string; description: string }>) => (
    <div style={{ marginBottom: '24px' }}>
      <Title level={5} style={{ marginBottom: '12px', color: '#1890ff' }}>
        {title}
      </Title>
      <div style={{ display: 'grid', gap: '8px' }}>
        {shortcuts.map(shortcut => (
          <div 
            key={shortcut.key}
            style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              padding: '8px 12px',
              background: '#fafafa',
              borderRadius: '4px',
              border: '1px solid #f0f0f0'
            }}
          >
            <Text style={{ fontSize: '13px' }}>{shortcut.description}</Text>
            {renderShortcutKey(shortcut.key)}
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <span>⌨️</span>
          <span>键盘快捷键</span>
        </div>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={600}
      style={{ top: 20 }}
      bodyStyle={{ maxHeight: '70vh', overflowY: 'auto' }}
    >
      <div style={{ padding: '8px 0' }}>
        <Text type="secondary" style={{ fontSize: '13px', marginBottom: '20px', display: 'block' }}>
          使用键盘快捷键可以大大提高您的工作效率。以下是所有可用的快捷键：
        </Text>

        {renderShortcutGroup('文件操作', groupedShortcuts.file)}
        
        {renderShortcutGroup('编辑操作', groupedShortcuts.edit)}
        
        {renderShortcutGroup('视图操作', groupedShortcuts.view)}
        
        {renderShortcutGroup('工作流操作', groupedShortcuts.workflow)}
        
        {renderShortcutGroup('帮助', groupedShortcuts.help)}

        <Divider />

        <div style={{ background: '#f8f9fa', padding: '12px', borderRadius: '6px', marginTop: '16px' }}>
          <Title level={5} style={{ margin: '0 0 8px 0', fontSize: '13px' }}>
            💡 提示
          </Title>
          <ul style={{ margin: 0, paddingLeft: '16px', fontSize: '12px', color: '#666' }}>
            <li>在输入框中时，快捷键会被禁用</li>
            <li>按住 Space 键可以拖拽画布</li>
            <li>使用鼠标滚轮可以缩放画布</li>
            <li>按 Ctrl+滚轮 可以精确缩放</li>
          </ul>
        </div>
      </div>
    </Modal>
  );
};

export default ShortcutsHelp;
