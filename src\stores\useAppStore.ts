import { create } from 'zustand';
import { v4 as uuidv4 } from 'uuid';
import { AppState, BaseNode, Connection, Workflow, CanvasState } from '../types';

interface AppStore extends AppState {
  // 节点操作
  addNode: (node: Omit<BaseNode, 'id'>) => void;
  removeNode: (nodeId: string) => void;
  updateNode: (nodeId: string, updates: Partial<BaseNode>) => void;
  selectNode: (nodeId: string, multiSelect?: boolean) => void;
  clearSelection: () => void;
  
  // 连接操作
  addConnection: (connection: Omit<Connection, 'id'>) => void;
  removeConnection: (connectionId: string) => void;
  
  // 画布操作
  updateCanvas: (updates: Partial<CanvasState>) => void;
  
  // 工作流操作
  saveWorkflow: () => void;
  loadWorkflow: (workflow: Workflow) => void;
  newWorkflow: () => void;
  
  // 历史操作
  undo: () => void;
  redo: () => void;
  pushHistory: () => void;
  
  // 处理状态
  setProcessing: (isProcessing: boolean) => void;
}

const createEmptyWorkflow = (): Workflow => ({
  id: uuidv4(),
  name: '新工作流',
  nodes: [],
  connections: [],
  createdAt: new Date(),
  updatedAt: new Date(),
});

const initialCanvasState: CanvasState = {
  zoom: 1,
  pan: { x: 0, y: 0 },
  gridVisible: true,
  snapToGrid: true,
};

export const useAppStore = create<AppStore>((set, get) => ({
  // 初始状态
  workflow: createEmptyWorkflow(),
  canvas: initialCanvasState,
  selectedNodes: [],
  clipboard: [],
  history: [],
  historyIndex: -1,
  isProcessing: false,

  // 节点操作
  addNode: (nodeData) => {
    const node: BaseNode = {
      ...nodeData,
      id: uuidv4(),
    };
    
    set((state) => ({
      workflow: {
        ...state.workflow,
        nodes: [...state.workflow.nodes, node],
        updatedAt: new Date(),
      },
    }));
    
    get().pushHistory();
  },

  removeNode: (nodeId) => {
    set((state) => ({
      workflow: {
        ...state.workflow,
        nodes: state.workflow.nodes.filter(node => node.id !== nodeId),
        connections: state.workflow.connections.filter(
          conn => conn.sourceNodeId !== nodeId && conn.targetNodeId !== nodeId
        ),
        updatedAt: new Date(),
      },
      selectedNodes: state.selectedNodes.filter(id => id !== nodeId),
    }));
    
    get().pushHistory();
  },

  updateNode: (nodeId, updates) => {
    set((state) => ({
      workflow: {
        ...state.workflow,
        nodes: state.workflow.nodes.map(node =>
          node.id === nodeId ? { ...node, ...updates } : node
        ),
        updatedAt: new Date(),
      },
    }));
  },

  selectNode: (nodeId, multiSelect = false) => {
    set((state) => {
      if (multiSelect) {
        const isSelected = state.selectedNodes.includes(nodeId);
        return {
          selectedNodes: isSelected
            ? state.selectedNodes.filter(id => id !== nodeId)
            : [...state.selectedNodes, nodeId],
        };
      } else {
        return {
          selectedNodes: [nodeId],
        };
      }
    });
  },

  clearSelection: () => {
    set({ selectedNodes: [] });
  },

  // 连接操作
  addConnection: (connectionData) => {
    const connection: Connection = {
      ...connectionData,
      id: uuidv4(),
    };
    
    set((state) => ({
      workflow: {
        ...state.workflow,
        connections: [...state.workflow.connections, connection],
        updatedAt: new Date(),
      },
    }));
    
    get().pushHistory();
  },

  removeConnection: (connectionId) => {
    set((state) => ({
      workflow: {
        ...state.workflow,
        connections: state.workflow.connections.filter(conn => conn.id !== connectionId),
        updatedAt: new Date(),
      },
    }));
    
    get().pushHistory();
  },

  // 画布操作
  updateCanvas: (updates) => {
    set((state) => ({
      canvas: { ...state.canvas, ...updates },
    }));
  },

  // 工作流操作
  saveWorkflow: () => {
    const { workflow } = get();
    // 这里可以添加保存到本地存储或服务器的逻辑
    localStorage.setItem('imageflow-workflow', JSON.stringify(workflow));
  },

  loadWorkflow: (workflow) => {
    set({
      workflow,
      selectedNodes: [],
      history: [],
      historyIndex: -1,
    });
  },

  newWorkflow: () => {
    set({
      workflow: createEmptyWorkflow(),
      selectedNodes: [],
      history: [],
      historyIndex: -1,
    });
  },

  // 历史操作
  pushHistory: () => {
    const { workflow, history, historyIndex } = get();
    const newHistory = history.slice(0, historyIndex + 1);
    newHistory.push(JSON.parse(JSON.stringify(workflow)));
    
    set({
      history: newHistory,
      historyIndex: newHistory.length - 1,
    });
  },

  undo: () => {
    const { history, historyIndex } = get();
    if (historyIndex > 0) {
      const previousWorkflow = history[historyIndex - 1];
      set({
        workflow: JSON.parse(JSON.stringify(previousWorkflow)),
        historyIndex: historyIndex - 1,
        selectedNodes: [],
      });
    }
  },

  redo: () => {
    const { history, historyIndex } = get();
    if (historyIndex < history.length - 1) {
      const nextWorkflow = history[historyIndex + 1];
      set({
        workflow: JSON.parse(JSON.stringify(nextWorkflow)),
        historyIndex: historyIndex + 1,
        selectedNodes: [],
      });
    }
  },

  // 处理状态
  setProcessing: (isProcessing) => {
    set({ isProcessing });
  },
}));
