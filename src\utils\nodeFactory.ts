import { v4 as uuidv4 } from 'uuid';
import { BaseNode, NodeConnection, NodePosition } from '../types';

/**
 * 节点工厂类 - 负责创建各种类型的节点
 */
export class NodeFactory {
  /**
   * 创建节点连接点
   */
  private static createConnection(
    type: 'input' | 'output',
    dataType: 'image' | 'number' | 'color' | 'boolean',
    label: string
  ): NodeConnection {
    return {
      id: uuidv4(),
      nodeId: '',
      type,
      dataType,
      label
    };
  }

  /**
   * 创建基础节点
   */
  private static createBaseNode(
    type: string,
    label: string,
    position: NodePosition,
    inputs: NodeConnection[] = [],
    outputs: NodeConnection[] = [],
    parameters: Record<string, any> = {}
  ): BaseNode {
    const node: BaseNode = {
      id: uuidv4(),
      type,
      position,
      size: { width: 160, height: 80 },
      inputs,
      outputs,
      parameters,
      label,
      selected: false
    };

    // 设置连接点的节点ID
    [...inputs, ...outputs].forEach(connection => {
      connection.nodeId = node.id;
    });

    return node;
  }

  /**
   * 创建图片上传节点
   */
  static createImageUploadNode(position: NodePosition): BaseNode {
    return this.createBaseNode(
      'image-upload',
      '图片上传',
      position,
      [], // 无输入
      [this.createConnection('output', 'image', '图片')],
      {
        file: null,
        preview: null
      }
    );
  }

  /**
   * 创建缩放节点
   */
  static createResizeNode(position: NodePosition): BaseNode {
    return this.createBaseNode(
      'resize',
      '缩放',
      position,
      [this.createConnection('input', 'image', '输入图片')],
      [this.createConnection('output', 'image', '输出图片')],
      {
        width: 800,
        height: 600,
        maintainAspectRatio: true,
        resizeMode: 'stretch' // stretch, fit, fill
      }
    );
  }

  /**
   * 创建旋转节点
   */
  static createRotateNode(position: NodePosition): BaseNode {
    return this.createBaseNode(
      'rotate',
      '旋转',
      position,
      [this.createConnection('input', 'image', '输入图片')],
      [this.createConnection('output', 'image', '输出图片')],
      {
        angle: 0,
        backgroundColor: '#ffffff',
        autoResize: true
      }
    );
  }

  /**
   * 创建裁剪节点
   */
  static createCropNode(position: NodePosition): BaseNode {
    return this.createBaseNode(
      'crop',
      '裁剪',
      position,
      [this.createConnection('input', 'image', '输入图片')],
      [this.createConnection('output', 'image', '输出图片')],
      {
        x: 0,
        y: 0,
        width: 100,
        height: 100,
        unit: 'pixel' // pixel, percent
      }
    );
  }

  /**
   * 创建翻转节点
   */
  static createFlipNode(position: NodePosition): BaseNode {
    return this.createBaseNode(
      'flip',
      '翻转',
      position,
      [this.createConnection('input', 'image', '输入图片')],
      [this.createConnection('output', 'image', '输出图片')],
      {
        horizontal: false,
        vertical: false
      }
    );
  }

  /**
   * 创建亮度对比度节点
   */
  static createBrightnessContrastNode(position: NodePosition): BaseNode {
    return this.createBaseNode(
      'brightness-contrast',
      '亮度对比度',
      position,
      [this.createConnection('input', 'image', '输入图片')],
      [this.createConnection('output', 'image', '输出图片')],
      {
        brightness: 0, // -100 to 100
        contrast: 0    // -100 to 100
      }
    );
  }

  /**
   * 创建色调饱和度节点
   */
  static createHueSaturationNode(position: NodePosition): BaseNode {
    return this.createBaseNode(
      'hue-saturation',
      '色调饱和度',
      position,
      [this.createConnection('input', 'image', '输入图片')],
      [this.createConnection('output', 'image', '输出图片')],
      {
        hue: 0,        // -180 to 180
        saturation: 0, // -100 to 100
        lightness: 0   // -100 to 100
      }
    );
  }

  /**
   * 创建色彩平衡节点
   */
  static createColorBalanceNode(position: NodePosition): BaseNode {
    return this.createBaseNode(
      'color-balance',
      '色彩平衡',
      position,
      [this.createConnection('input', 'image', '输入图片')],
      [this.createConnection('output', 'image', '输出图片')],
      {
        shadows: { cyan: 0, magenta: 0, yellow: 0 },
        midtones: { cyan: 0, magenta: 0, yellow: 0 },
        highlights: { cyan: 0, magenta: 0, yellow: 0 }
      }
    );
  }

  /**
   * 创建曲线调整节点
   */
  static createCurvesNode(position: NodePosition): BaseNode {
    return this.createBaseNode(
      'curves',
      '曲线调整',
      position,
      [this.createConnection('input', 'image', '输入图片')],
      [this.createConnection('output', 'image', '输出图片')],
      {
        points: [
          { x: 0, y: 0 },
          { x: 255, y: 255 }
        ]
      }
    );
  }

  /**
   * 创建色阶调整节点
   */
  static createLevelsNode(position: NodePosition): BaseNode {
    return this.createBaseNode(
      'levels',
      '色阶调整',
      position,
      [this.createConnection('input', 'image', '输入图片')],
      [this.createConnection('output', 'image', '输出图片')],
      {
        inputBlack: 0,
        inputWhite: 255,
        gamma: 1.0,
        outputBlack: 0,
        outputWhite: 255
      }
    );
  }

  /**
   * 创建模糊节点
   */
  static createBlurNode(position: NodePosition): BaseNode {
    return this.createBaseNode(
      'blur',
      '模糊',
      position,
      [this.createConnection('input', 'image', '输入图片')],
      [this.createConnection('output', 'image', '输出图片')],
      {
        radius: 5,
        type: 'gaussian' // gaussian, motion, radial
      }
    );
  }

  /**
   * 创建锐化节点
   */
  static createSharpenNode(position: NodePosition): BaseNode {
    return this.createBaseNode(
      'sharpen',
      '锐化',
      position,
      [this.createConnection('input', 'image', '输入图片')],
      [this.createConnection('output', 'image', '输出图片')],
      {
        amount: 1.0,
        radius: 1.0,
        threshold: 0
      }
    );
  }

  /**
   * 创建噪点节点
   */
  static createNoiseNode(position: NodePosition): BaseNode {
    return this.createBaseNode(
      'noise',
      '噪点',
      position,
      [this.createConnection('input', 'image', '输入图片')],
      [this.createConnection('output', 'image', '输出图片')],
      {
        amount: 10,
        type: 'uniform', // uniform, gaussian
        monochromatic: false
      }
    );
  }

  /**
   * 创建边缘检测节点
   */
  static createEdgeDetectionNode(position: NodePosition): BaseNode {
    return this.createBaseNode(
      'edge-detection',
      '边缘检测',
      position,
      [this.createConnection('input', 'image', '输入图片')],
      [this.createConnection('output', 'image', '输出图片')],
      {
        threshold: 50,
        method: 'sobel' // sobel, canny, laplacian
      }
    );
  }

  /**
   * 创建浮雕节点
   */
  static createEmbossNode(position: NodePosition): BaseNode {
    return this.createBaseNode(
      'emboss',
      '浮雕',
      position,
      [this.createConnection('input', 'image', '输入图片')],
      [this.createConnection('output', 'image', '输出图片')],
      {
        angle: 45,
        height: 3,
        amount: 100
      }
    );
  }

  /**
   * 创建图片导出节点
   */
  static createImageExportNode(position: NodePosition): BaseNode {
    return this.createBaseNode(
      'image-export',
      '图片导出',
      position,
      [this.createConnection('input', 'image', '输入图片')],
      [], // 无输出
      {
        format: 'png', // png, jpg, webp
        quality: 90,
        filename: 'output'
      }
    );
  }

  /**
   * 根据节点类型创建节点
   */
  static createNode(nodeType: string, position: NodePosition): BaseNode {
    switch (nodeType) {
      case 'image-upload':
        return this.createImageUploadNode(position);
      case 'resize':
        return this.createResizeNode(position);
      case 'rotate':
        return this.createRotateNode(position);
      case 'crop':
        return this.createCropNode(position);
      case 'flip':
        return this.createFlipNode(position);
      case 'brightness-contrast':
        return this.createBrightnessContrastNode(position);
      case 'hue-saturation':
        return this.createHueSaturationNode(position);
      case 'color-balance':
        return this.createColorBalanceNode(position);
      case 'curves':
        return this.createCurvesNode(position);
      case 'levels':
        return this.createLevelsNode(position);
      case 'blur':
        return this.createBlurNode(position);
      case 'sharpen':
        return this.createSharpenNode(position);
      case 'noise':
        return this.createNoiseNode(position);
      case 'edge-detection':
        return this.createEdgeDetectionNode(position);
      case 'emboss':
        return this.createEmbossNode(position);
      case 'image-export':
        return this.createImageExportNode(position);
      default:
        throw new Error(`Unknown node type: ${nodeType}`);
    }
  }
}
