import React, { useState, useEffect } from 'react';
import {
  Button,
  Space,
  Divider,
  Tooltip,
  Dropdown,
  Typography,
  message
} from 'antd';
import {
  FileOutlined,
  FolderOpenOutlined,
  SaveOutlined,
  UndoOutlined,
  RedoOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  SettingOutlined,
  QuestionCircleOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined
} from '@ant-design/icons';
import { useAppStore } from '../../stores/useAppStore';
import ShortcutsHelp from '../Help/ShortcutsHelp';
import WorkflowList from '../Workflow/WorkflowList';
import { initializeShortcuts, cleanupShortcuts } from '../../utils/keyboardShortcuts';
import { WorkflowManager } from '../../utils/workflowManager';
import './Toolbar.css';

const { Text } = Typography;

const Toolbar: React.FC = () => {
  const {
    workflow,
    canvas,
    history,
    historyIndex,
    isProcessing,
    selectedNodes,
    newWorkflow,
    saveWorkflow,
    undo,
    redo,
    updateCanvas,
    setProcessing,
    removeNode,
    clearSelection
  } = useAppStore();

  const [shortcutsHelpVisible, setShortcutsHelpVisible] = useState(false);
  const [workflowListVisible, setWorkflowListVisible] = useState(false);
  const [workflowListMode, setWorkflowListMode] = useState<'open' | 'manage'>('open');

  // 初始化快捷键
  useEffect(() => {
    const shortcuts = {
      newWorkflow: () => {
        newWorkflow();
        message.success('新建工作流');
      },
      openWorkflow: () => {
        // TODO: 实现打开工作流
        message.info('打开工作流功能开发中');
      },
      saveWorkflow: () => {
        saveWorkflow();
        message.success('工作流已保存');
      },
      undo: () => {
        if (historyIndex > 0) {
          undo();
          message.success('已撤销');
        }
      },
      redo: () => {
        if (historyIndex < history.length - 1) {
          redo();
          message.success('已重做');
        }
      },
      deleteSelected: () => {
        if (selectedNodes.length > 0) {
          selectedNodes.forEach(nodeId => removeNode(nodeId));
          message.success(`已删除 ${selectedNodes.length} 个节点`);
        }
      },
      selectAll: () => {
        // TODO: 实现全选
        message.info('全选功能开发中');
      },
      copy: () => {
        // TODO: 实现复制
        message.info('复制功能开发中');
      },
      paste: () => {
        // TODO: 实现粘贴
        message.info('粘贴功能开发中');
      },
      cut: () => {
        // TODO: 实现剪切
        message.info('剪切功能开发中');
      },
      duplicate: () => {
        // TODO: 实现复制节点
        message.info('复制节点功能开发中');
      },
      clearSelection: () => {
        clearSelection();
      },
      zoomIn: () => {
        updateCanvas({ zoom: Math.min(canvas.zoom * 1.2, 5) });
      },
      zoomOut: () => {
        updateCanvas({ zoom: Math.max(canvas.zoom / 1.2, 0.1) });
      },
      resetZoom: () => {
        updateCanvas({ zoom: 1, pan: { x: 0, y: 0 } });
        message.success('已重置缩放');
      },
      fitToScreen: () => {
        // TODO: 实现适应屏幕
        updateCanvas({ zoom: 1, pan: { x: 0, y: 0 } });
        message.success('已适应屏幕');
      },
      toggleFullscreen: () => {
        // TODO: 实现全屏切换
        message.info('全屏切换功能开发中');
      },
      showHelp: () => {
        setShortcutsHelpVisible(true);
      },
      executeWorkflow: () => {
        setProcessing(!isProcessing);
        message.success(isProcessing ? '已停止处理' : '开始处理');
      }
    };

    initializeShortcuts(shortcuts);

    return () => {
      cleanupShortcuts();
    };
  }, [
    newWorkflow, saveWorkflow, undo, redo, updateCanvas, setProcessing,
    removeNode, clearSelection, canvas.zoom, historyIndex, history.length,
    selectedNodes, isProcessing
  ]);

  const fileMenuItems = [
    {
      key: 'new',
      label: '新建工作流',
      icon: <FileOutlined />,
      onClick: newWorkflow,
    },
    {
      key: 'open',
      label: '打开工作流',
      icon: <FolderOpenOutlined />,
      onClick: () => {
        // TODO: 实现文件打开对话框
        console.log('打开工作流');
      },
    },
    {
      key: 'save',
      label: '保存工作流',
      icon: <SaveOutlined />,
      onClick: saveWorkflow,
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'export',
      label: '导出图片',
      onClick: () => {
        // TODO: 实现图片导出
        console.log('导出图片');
      },
    },
  ];

  const viewMenuItems = [
    {
      key: 'zoom-in',
      label: '放大',
      icon: <ZoomInOutlined />,
      onClick: () => updateCanvas({ zoom: Math.min(canvas.zoom * 1.2, 5) }),
    },
    {
      key: 'zoom-out',
      label: '缩小',
      icon: <ZoomOutOutlined />,
      onClick: () => updateCanvas({ zoom: Math.max(canvas.zoom / 1.2, 0.1) }),
    },
    {
      key: 'zoom-reset',
      label: '重置缩放',
      onClick: () => updateCanvas({ zoom: 1, pan: { x: 0, y: 0 } }),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'grid',
      label: canvas.gridVisible ? '隐藏网格' : '显示网格',
      onClick: () => updateCanvas({ gridVisible: !canvas.gridVisible }),
    },
    {
      key: 'snap',
      label: canvas.snapToGrid ? '关闭对齐' : '开启对齐',
      onClick: () => updateCanvas({ snapToGrid: !canvas.snapToGrid }),
    },
  ];

  const helpMenuItems = [
    {
      key: 'shortcuts',
      label: '快捷键',
      onClick: () => {
        setShortcutsHelpVisible(true);
      },
    },
    {
      key: 'tutorial',
      label: '使用教程',
      onClick: () => {
        // TODO: 打开教程
        message.info('使用教程功能开发中');
      },
    },
    {
      key: 'about',
      label: '关于',
      onClick: () => {
        // TODO: 显示关于对话框
        message.info('关于对话框功能开发中');
      },
    },
  ];

  const handleProcessToggle = () => {
    setProcessing(!isProcessing);
    // TODO: 实际的处理逻辑
  };

  return (
    <div className="toolbar">
      <div className="toolbar-left">
        <div className="toolbar-logo">
          <Text strong style={{ fontSize: '18px', color: '#1890ff' }}>
            ImageFlow
          </Text>
        </div>
        
        <Divider type="vertical" />
        
        <Space size="small">
          <Dropdown menu={{ items: fileMenuItems }} placement="bottomLeft">
            <Button type="text">文件</Button>
          </Dropdown>
          
          <Dropdown menu={{ items: viewMenuItems }} placement="bottomLeft">
            <Button type="text">视图</Button>
          </Dropdown>
          
          <Dropdown menu={{ items: helpMenuItems }} placement="bottomLeft">
            <Button type="text">帮助</Button>
          </Dropdown>
        </Space>
      </div>

      <div className="toolbar-center">
        <Space size="small">
          <Tooltip title="撤销">
            <Button
              type="text"
              icon={<UndoOutlined />}
              disabled={historyIndex <= 0}
              onClick={undo}
            />
          </Tooltip>
          
          <Tooltip title="重做">
            <Button
              type="text"
              icon={<RedoOutlined />}
              disabled={historyIndex >= history.length - 1}
              onClick={redo}
            />
          </Tooltip>
          
          <Divider type="vertical" />
          
          <Tooltip title={isProcessing ? "暂停处理" : "开始处理"}>
            <Button
              type={isProcessing ? "primary" : "default"}
              icon={isProcessing ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              onClick={handleProcessToggle}
            >
              {isProcessing ? "暂停" : "处理"}
            </Button>
          </Tooltip>
        </Space>
      </div>

      <div className="toolbar-right">
        <Space size="small">
          <Text type="secondary" style={{ fontSize: '12px' }}>
            缩放: {Math.round(canvas.zoom * 100)}%
          </Text>
          
          <Divider type="vertical" />
          
          <Tooltip title="设置">
            <Button
              type="text"
              icon={<SettingOutlined />}
              onClick={() => {
                // TODO: 打开设置面板
                console.log('设置');
              }}
            />
          </Tooltip>
          
          <Tooltip title="帮助">
            <Button
              type="text"
              icon={<QuestionCircleOutlined />}
              onClick={() => setShortcutsHelpVisible(true)}
            />
          </Tooltip>
        </Space>
      </div>

      {/* 快捷键帮助对话框 */}
      <ShortcutsHelp
        visible={shortcutsHelpVisible}
        onClose={() => setShortcutsHelpVisible(false)}
      />
    </div>
  );
};

export default Toolbar;
