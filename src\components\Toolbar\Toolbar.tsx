import React from 'react';
import { 
  But<PERSON>, 
  Space, 
  Divider, 
  Tooltip, 
  Dropdown,
  Typography
} from 'antd';
import {
  FileOutlined,
  FolderOpenOutlined,
  SaveOutlined,
  UndoOutlined,
  RedoOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  SettingOutlined,
  QuestionCircleOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined
} from '@ant-design/icons';
import { useAppStore } from '../../stores/useAppStore';
import './Toolbar.css';

const { Text } = Typography;

const Toolbar: React.FC = () => {
  const {
    workflow,
    canvas,
    history,
    historyIndex,
    isProcessing,
    newWorkflow,
    saveWorkflow,
    undo,
    redo,
    updateCanvas,
    setProcessing
  } = useAppStore();

  const fileMenuItems = [
    {
      key: 'new',
      label: '新建工作流',
      icon: <FileOutlined />,
      onClick: newWorkflow,
    },
    {
      key: 'open',
      label: '打开工作流',
      icon: <FolderOpenOutlined />,
      onClick: () => {
        // TODO: 实现文件打开对话框
        console.log('打开工作流');
      },
    },
    {
      key: 'save',
      label: '保存工作流',
      icon: <SaveOutlined />,
      onClick: saveWorkflow,
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'export',
      label: '导出图片',
      onClick: () => {
        // TODO: 实现图片导出
        console.log('导出图片');
      },
    },
  ];

  const viewMenuItems = [
    {
      key: 'zoom-in',
      label: '放大',
      icon: <ZoomInOutlined />,
      onClick: () => updateCanvas({ zoom: Math.min(canvas.zoom * 1.2, 5) }),
    },
    {
      key: 'zoom-out',
      label: '缩小',
      icon: <ZoomOutOutlined />,
      onClick: () => updateCanvas({ zoom: Math.max(canvas.zoom / 1.2, 0.1) }),
    },
    {
      key: 'zoom-reset',
      label: '重置缩放',
      onClick: () => updateCanvas({ zoom: 1, pan: { x: 0, y: 0 } }),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'grid',
      label: canvas.gridVisible ? '隐藏网格' : '显示网格',
      onClick: () => updateCanvas({ gridVisible: !canvas.gridVisible }),
    },
    {
      key: 'snap',
      label: canvas.snapToGrid ? '关闭对齐' : '开启对齐',
      onClick: () => updateCanvas({ snapToGrid: !canvas.snapToGrid }),
    },
  ];

  const helpMenuItems = [
    {
      key: 'shortcuts',
      label: '快捷键',
      onClick: () => {
        // TODO: 显示快捷键帮助
        console.log('快捷键帮助');
      },
    },
    {
      key: 'tutorial',
      label: '使用教程',
      onClick: () => {
        // TODO: 打开教程
        console.log('使用教程');
      },
    },
    {
      key: 'about',
      label: '关于',
      onClick: () => {
        // TODO: 显示关于对话框
        console.log('关于');
      },
    },
  ];

  const handleProcessToggle = () => {
    setProcessing(!isProcessing);
    // TODO: 实际的处理逻辑
  };

  return (
    <div className="toolbar">
      <div className="toolbar-left">
        <div className="toolbar-logo">
          <Text strong style={{ fontSize: '18px', color: '#1890ff' }}>
            ImageFlow
          </Text>
        </div>
        
        <Divider type="vertical" />
        
        <Space size="small">
          <Dropdown menu={{ items: fileMenuItems }} placement="bottomLeft">
            <Button type="text">文件</Button>
          </Dropdown>
          
          <Dropdown menu={{ items: viewMenuItems }} placement="bottomLeft">
            <Button type="text">视图</Button>
          </Dropdown>
          
          <Dropdown menu={{ items: helpMenuItems }} placement="bottomLeft">
            <Button type="text">帮助</Button>
          </Dropdown>
        </Space>
      </div>

      <div className="toolbar-center">
        <Space size="small">
          <Tooltip title="撤销">
            <Button
              type="text"
              icon={<UndoOutlined />}
              disabled={historyIndex <= 0}
              onClick={undo}
            />
          </Tooltip>
          
          <Tooltip title="重做">
            <Button
              type="text"
              icon={<RedoOutlined />}
              disabled={historyIndex >= history.length - 1}
              onClick={redo}
            />
          </Tooltip>
          
          <Divider type="vertical" />
          
          <Tooltip title={isProcessing ? "暂停处理" : "开始处理"}>
            <Button
              type={isProcessing ? "primary" : "default"}
              icon={isProcessing ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              onClick={handleProcessToggle}
            >
              {isProcessing ? "暂停" : "处理"}
            </Button>
          </Tooltip>
        </Space>
      </div>

      <div className="toolbar-right">
        <Space size="small">
          <Text type="secondary" style={{ fontSize: '12px' }}>
            缩放: {Math.round(canvas.zoom * 100)}%
          </Text>
          
          <Divider type="vertical" />
          
          <Tooltip title="设置">
            <Button
              type="text"
              icon={<SettingOutlined />}
              onClick={() => {
                // TODO: 打开设置面板
                console.log('设置');
              }}
            />
          </Tooltip>
          
          <Tooltip title="帮助">
            <Button
              type="text"
              icon={<QuestionCircleOutlined />}
              onClick={() => {
                // TODO: 打开帮助
                console.log('帮助');
              }}
            />
          </Tooltip>
        </Space>
      </div>
    </div>
  );
};

export default Toolbar;
